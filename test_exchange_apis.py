"""
Test script for exchange APIs
"""
import json
from decimal import Decimal
from datetime import datetime

from exchange_apis.core.types import (
    Location, TradeType, Timestamp, AssetAmount, Price, Fee, Asset
)
from exchange_apis.core.trade import Trade
from exchange_apis.exchanges.kraken import <PERSON>rakenExchange
from exchange_apis.exchanges.binance import BinanceExchange
from exchange_apis.exchanges.coinbase import CoinbaseExchange


def test_trade_object():
    """Test Trade object functionality"""
    print("=== Testing Trade Object ===")

    # Create a sample trade
    trade = Trade(
        timestamp=Timestamp(int(datetime.now().timestamp())),
        location=Location.KRAKEN,
        base_asset=Asset('BTC'),
        quote_asset=Asset('USD'),
        trade_type=TradeType.BUY,
        amount=AssetAmount(Decimal('0.5')),
        rate=Price(Decimal('50000.00')),
        fee=Fee(Decimal('25.00')),
        fee_currency=Asset('USD'),
        link='test_trade_123',
        notes='Test trade'
    )

    print(f"Trade: {trade}")
    print(f"Trade ID: {trade.identifier}")

    # Test serialization
    trade_dict = trade.to_dict()
    print(f"Trade dict: {json.dumps(trade_dict, indent=2)}")

    # Test deserialization
    trade_from_dict = Trade.from_dict(trade_dict)
    print(f"Reconstructed trade: {trade_from_dict}")

    # Test JSON serialization
    trade_json = trade.to_json()
    trade_from_json = Trade.from_json(trade_json)
    print(f"JSON round-trip successful: {trade == trade_from_json}")

    print()


def test_exchange_initialization():
    """Test exchange initialization"""
    print("=== Testing Exchange Initialization ===")

    # Test Kraken
    try:
        kraken = KrakenExchange(
            name="test_kraken",
            api_key="test_key",
            api_secret=b"test_secret",
        )
        print(f"✅ Kraken initialized: {kraken}")
    except Exception as e:
        print(f"❌ Kraken initialization failed: {e}")

    # Test Binance
    try:
        binance = BinanceExchange(
            name="test_binance",
            api_key="test_key",
            api_secret=b"test_secret",
        )
        print(f"✅ Binance initialized: {binance}")
    except Exception as e:
        print(f"❌ Binance initialization failed: {e}")

    # Test Coinbase
    try:
        coinbase = CoinbaseExchange(
            name="test_coinbase",
            api_key="test_key",
            api_secret=b"test_secret",
            passphrase="test_passphrase",
        )
        print(f"✅ Coinbase initialized: {coinbase}")
    except Exception as e:
        print(f"❌ Coinbase initialization failed: {e}")

    print()


def test_type_conversions():
    """Test type conversions"""
    print("=== Testing Type Conversions ===")

    # Test TradeType
    try:
        buy_type = TradeType.from_string("buy")
        sell_type = TradeType.from_string("sell")
        print(f"✅ TradeType conversions: {buy_type}, {sell_type}")
    except Exception as e:
        print(f"❌ TradeType conversion failed: {e}")

    # Test Location
    try:
        kraken_loc = Location.from_string("KRAKEN")
        binance_loc = Location.from_string("binance")
        print(f"✅ Location conversions: {kraken_loc}, {binance_loc}")
    except Exception as e:
        print(f"❌ Location conversion failed: {e}")

    # Test Asset
    try:
        btc = Asset('BTC', 'BTC', 'Bitcoin')
        eth = Asset('ETH')
        print(f"✅ Asset creation: {btc}, {eth}")
        print(f"✅ Asset equality: {btc == Asset('BTC')}")
    except Exception as e:
        print(f"❌ Asset creation failed: {e}")

    print()


def test_mock_trade_parsing():
    """Test trade parsing with mock data"""
    print("=== Testing Mock Trade Parsing ===")

    # Mock Kraken trade data
    kraken_mock = {
        'pair': 'XXBTZUSD',
        'type': 'buy',
        'vol': '0.5',
        'price': '50000.00',
        'fee': '25.00',
        'time': str(datetime.now().timestamp()),
    }

    try:
        kraken = KrakenExchange("test", "key", b"secret")
        trade = kraken._parse_trade_data(kraken_mock)
        if trade:
            print(f"✅ Kraken trade parsed: {trade}")
        else:
            print("❌ Kraken trade parsing returned None")
    except Exception as e:
        print(f"❌ Kraken trade parsing failed: {e}")

    # Mock Binance trade data
    binance_mock = {
        'symbol': 'BTCUSDT',
        'isBuyer': True,
        'qty': '0.5',
        'price': '50000.00',
        'commission': '0.001',
        'commissionAsset': 'BTC',
        'time': int(datetime.now().timestamp() * 1000),
        'id': '12345',
    }

    try:
        binance = BinanceExchange("test", "key", b"secret")
        trade = binance._parse_trade_data(binance_mock)
        if trade:
            print(f"✅ Binance trade parsed: {trade}")
        else:
            print("❌ Binance trade parsing returned None")
    except Exception as e:
        print(f"❌ Binance trade parsing failed: {e}")

    print()


def test_symbol_parsing():
    """Test symbol/pair parsing"""
    print("=== Testing Symbol Parsing ===")

    # Test Kraken pair parsing
    try:
        kraken = KrakenExchange("test", "key", b"secret")

        test_pairs = ['XXBTZUSD', 'XETHZEUR', 'ADAUSD', 'DOTUSD', 'LINKETH', 'ATOMEUR']
        for pair in test_pairs:
            base, quote = kraken.pair_parser.parse_pair(pair)
            print(f"✅ Kraken {pair} -> {base.identifier}/{quote.identifier}")
    except Exception as e:
        print(f"❌ Kraken pair parsing failed: {e}")

    # Test Binance symbol parsing
    try:
        binance = BinanceExchange("test", "key", b"secret")

        test_symbols = ['BTCUSDT', 'ETHBTC', 'ADAUSDC', 'BNBEUR', 'DOTBUSD', 'LINKUSDT']
        for symbol in test_symbols:
            base, quote = binance.pair_parser.parse_symbol(symbol)
            print(f"✅ Binance {symbol} -> {base.identifier}/{quote.identifier}")
    except Exception as e:
        print(f"❌ Binance symbol parsing failed: {e}")

    print()


def main():
    """Run all tests"""
    print("Exchange APIs Test Suite")
    print("=" * 50)

    test_trade_object()
    test_exchange_initialization()
    test_type_conversions()
    test_mock_trade_parsing()
    test_symbol_parsing()

    print("=" * 50)
    print("Test suite completed!")
    print("\nTo test with real API credentials:")
    print("1. Set environment variables for your exchange API keys")
    print("2. Run: python example_usage.py")


if __name__ == "__main__":
    main()
