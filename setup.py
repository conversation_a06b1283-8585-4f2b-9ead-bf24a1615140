"""
Setup script for exchange APIs library
"""
from setuptools import setup, find_packages

with open("EXCHANGE_APIS_README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="exchange-apis",
    version="1.0.0",
    author="Extracted from <PERSON><PERSON><PERSON><PERSON><PERSON>",
    description="Minimal cryptocurrency exchange APIs for trade export",
    long_description=long_description,
    long_description_content_type="text/markdown",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: GNU Affero General Public License v3",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=[
        "requests>=2.25.0",
    ],
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
        ],
    },
    entry_points={
        "console_scripts": [
            "exchange-apis-test=test_exchange_apis:main",
        ],
    },
)
