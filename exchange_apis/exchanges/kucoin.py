"""
KuCoin exchange implementation
"""
import time
import hashlib
import hmac
import base64
from typing import List, Dict, Any, Optional, Tu<PERSON>
from decimal import Decimal
from http import HTTPStatus

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import parse_decimal_safe, make_request
from ..core.asset_converters import asset_from_exchange
from .base import BaseExchange


class KuCoinExchange(BaseExchange):
    """KuCoin exchange implementation"""
    
    def __init__(self, name: str, api_key: str, api_secret: bytes, passphrase: str, **kwargs):
        super().__init__(
            name=name,
            location=Location.KUCOIN,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=1.0,  # 1 call per second
            base_url="https://api.kucoin.com",
        )
        
        # KuCoin specific settings
        self.api_passphrase = passphrase
        self.api_version = "v1"
        
    def validate_api_credentials(self) -> <PERSON><PERSON>[bool, str]:
        """Validate KuCoin API credentials"""
        try:
            response = self._make_authenticated_request('api/v1/accounts')
            if response.get('code') != '200000':
                return False, f"API Error: {response.get('msg', 'Unknown error')}"
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"
    
    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from KuCoin"""
        trades = []
        
        try:
            # KuCoin uses milliseconds
            start_ms = start_timestamp * 1000
            end_ms = end_timestamp * 1000
            
            # Query fills (executed trades)
            page_size = 500  # KuCoin max page size
            current_page = 1
            
            while True:
                options = {
                    'currentPage': current_page,
                    'pageSize': page_size,
                    'startAt': start_ms,
                    'endAt': end_ms,
                    'tradeType': 'TRADE',
                }
                
                response = self._make_authenticated_request('api/v1/fills', params=options)
                
                if response.get('code') != '200000':
                    break
                
                data = response.get('data', {})
                items = data.get('items', [])
                
                for trade_data in items:
                    trade = self._parse_trade_data(trade_data)
                    if trade:
                        trades.append(trade)
                
                # Check if we have more pages
                total_pages = data.get('totalPage', 1)
                if current_page >= total_pages:
                    break
                
                current_page += 1
            
        except Exception as e:
            print(f"Error querying KuCoin trades: {e}")
        
        return sorted(trades, key=lambda t: t.timestamp)
    
    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate KuCoin authentication headers"""
        if not self.api_secret:
            return {}
        
        # KuCoin uses millisecond timestamps
        timestamp = str(int(time.time() * 1000))
        
        # Create the message for signature
        api_path = f"/{endpoint.lstrip('/')}"
        query_string = ''
        if params:
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            if query_string:
                api_path += f"?{query_string}"
        
        body = ''
        if data:
            import json
            body = json.dumps(data)
        
        message = f"{timestamp}{method.upper()}{api_path}{body}"
        
        # Create signature
        signature = base64.b64encode(
            hmac.new(
                self.api_secret,
                message.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
        
        # Create passphrase signature
        passphrase_signature = base64.b64encode(
            hmac.new(
                self.api_secret,
                self.api_passphrase.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
        
        return {
            'Content-Type': 'application/json',
            'KC-API-KEY': self.api_key,
            'KC-API-SIGN': signature,
            'KC-API-TIMESTAMP': timestamp,
            'KC-API-PASSPHRASE': passphrase_signature,
            'KC-API-KEY-VERSION': '2',
        }
    
    def _parse_trade_data(self, raw_trade: Dict[str, Any]) -> Optional[Trade]:
        """Parse KuCoin trade data"""
        try:
            # KuCoin fill format
            trade_id = str(raw_trade['tradeId'])
            timestamp = Timestamp(int(raw_trade['createdAt']) // 1000)  # Convert from ms
            
            # Parse pair (e.g., "BTC-USDT")
            symbol = raw_trade['symbol']
            base_asset, quote_asset = self._parse_kucoin_pair(symbol)
            
            # Trade details
            side = raw_trade['side']
            trade_type = TradeType.BUY if side == 'buy' else TradeType.SELL
            
            amount = parse_decimal_safe(str(raw_trade['size']))
            price = parse_decimal_safe(str(raw_trade['price']))
            fee_amount = parse_decimal_safe(str(raw_trade['fee']))
            fee_currency_symbol = raw_trade['feeCurrency']
            
            # Parse fee
            fee = Fee(Decimal(str(fee_amount))) if fee_amount > 0 else None
            fee_currency = asset_from_exchange(fee_currency_symbol, self.location) if fee else None
            
            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=base_asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=AssetAmount(Decimal(str(amount))),
                rate=Price(Decimal(str(price))),
                fee=fee,
                fee_currency=fee_currency,
                link=trade_id,
            )
            
        except Exception as e:
            print(f"Error parsing KuCoin trade: {e}")
            return None
    
    def _parse_kucoin_pair(self, symbol: str) -> Tuple[Asset, Asset]:
        """Parse KuCoin trading pair"""
        # KuCoin uses dash separator (e.g., "BTC-USDT")
        if '-' in symbol:
            base_str, quote_str = symbol.split('-', 1)
        else:
            # Fallback for unexpected format
            raise ValueError(f"Unexpected KuCoin pair format: {symbol}")
        
        # Convert to assets
        base_asset = asset_from_exchange(base_str, self.location)
        quote_asset = asset_from_exchange(quote_str, self.location)
        
        return base_asset, quote_asset
