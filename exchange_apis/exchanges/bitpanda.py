"""
Bitpanda exchange implementation
"""
import time
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from urllib.parse import urlencode
from http import HTTPStatus

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import parse_decimal_safe, make_request
from ..core.asset_converters import asset_from_exchange
from .base import BaseExchange


class BitpandaExchange(BaseExchange):
    """Bitpanda exchange implementation"""
    
    def __init__(self, name: str, api_key: str, api_secret: bytes, **kwargs):
        super().__init__(
            name=name,
            location=Location.BITPANDA,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=1.0,  # 1 call per second
            base_url="https://api.bitpanda.com",
        )
        
        # Bitpanda specific settings
        self.api_version = "v1"
        self.cryptocoin_map: Dict[str, str] = {}
        self.fiat_map: Dict[str, str] = {}
        
    def validate_api_credentials(self) -> Tuple[bool, str]:
        """Validate Bitpanda API credentials"""
        try:
            response = self._make_authenticated_request('v1/wallets')
            if 'errors' in response:
                return False, f"API Error: {response['errors']}"
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"
    
    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from Bitpanda"""
        trades = []
        
        try:
            # First load asset mappings
            self._load_asset_mappings()
            
            # Query trades with pagination
            page = 1
            page_size = 100
            
            while True:
                params = {
                    'page': page,
                    'page_size': page_size,
                }
                
                response = self._make_authenticated_request('v1/trades', params=params)
                
                if 'data' not in response:
                    break
                
                data = response['data']
                if not data:
                    break
                
                for trade_data in data:
                    trade = self._parse_trade_data(trade_data)
                    if trade and start_timestamp <= trade.timestamp <= end_timestamp:
                        trades.append(trade)
                
                # Check if we have more pages
                meta = response.get('meta', {})
                total_count = meta.get('total_count', 0)
                current_count = page * page_size
                
                if current_count >= total_count:
                    break
                
                page += 1
            
        except Exception as e:
            print(f"Error querying Bitpanda trades: {e}")
        
        return sorted(trades, key=lambda t: t.timestamp)
    
    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate Bitpanda authentication headers"""
        # Bitpanda uses simple API key authentication
        return {
            'X-API-KEY': self.api_key,
            'Content-Type': 'application/json',
        }
    
    def _load_asset_mappings(self):
        """Load asset mappings from Bitpanda API"""
        try:
            # Load crypto wallets
            wallets_response = self._make_authenticated_request('v1/wallets')
            if 'data' in wallets_response:
                for wallet in wallets_response['data']:
                    if 'attributes' in wallet:
                        cryptocoin_id = wallet['attributes'].get('cryptocoin_id')
                        cryptocoin_symbol = wallet['attributes'].get('cryptocoin_symbol')
                        if cryptocoin_id and cryptocoin_symbol:
                            self.cryptocoin_map[cryptocoin_id] = cryptocoin_symbol
            
            # Load fiat wallets
            fiat_response = self._make_authenticated_request('v1/fiatwallets')
            if 'data' in fiat_response:
                for wallet in fiat_response['data']:
                    if 'attributes' in wallet:
                        fiat_id = wallet['attributes'].get('fiat_id')
                        fiat_symbol = wallet['attributes'].get('fiat_symbol')
                        if fiat_id and fiat_symbol:
                            self.fiat_map[fiat_id] = fiat_symbol
                            
        except Exception as e:
            print(f"Error loading Bitpanda asset mappings: {e}")
    
    def _parse_trade_data(self, raw_trade: Dict[str, Any]) -> Optional[Trade]:
        """Parse Bitpanda trade data"""
        try:
            # Bitpanda trade format
            if raw_trade.get('type') != 'trade':
                return None
            
            attributes = raw_trade.get('attributes', {})
            if attributes.get('status') != 'finished':
                return None
            
            trade_id = raw_trade['id']
            
            # Parse timestamp
            time_info = attributes.get('time', {})
            timestamp = Timestamp(int(time_info.get('unix', time.time())))
            
            # Get assets
            cryptocoin_id = attributes.get('cryptocoin_id')
            fiat_id = attributes.get('fiat_id')
            
            crypto_symbol = self.cryptocoin_map.get(cryptocoin_id, 'UNKNOWN')
            fiat_symbol = self.fiat_map.get(fiat_id, 'EUR')
            
            base_asset = asset_from_exchange(crypto_symbol, self.location)
            quote_asset = asset_from_exchange(fiat_symbol, self.location)
            
            # Trade details
            trade_type_str = attributes.get('type', 'buy')
            trade_type = TradeType.BUY if trade_type_str == 'buy' else TradeType.SELL
            
            amount = parse_decimal_safe(str(attributes.get('amount_cryptocoin', '0')))
            price = parse_decimal_safe(str(attributes.get('price', '0')))
            
            # Fee (Bitpanda uses BEST token for fees)
            fee_amount = Decimal('0')
            fee_currency = asset_from_exchange('BEST', self.location)
            
            if attributes.get('bfc_used') is True:
                best_fee_info = attributes.get('best_fee_collection', {})
                if best_fee_info:
                    wallet_tx = best_fee_info.get('attributes', {}).get('wallet_transaction', {})
                    fee_amount = parse_decimal_safe(str(wallet_tx.get('attributes', {}).get('fee', '0')))
            
            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=base_asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=AssetAmount(Decimal(str(amount))),
                rate=Price(Decimal(str(price))),
                fee=Fee(Decimal(str(fee_amount))) if fee_amount > 0 else None,
                fee_currency=fee_currency,
                link=trade_id,
            )
            
        except Exception as e:
            print(f"Error parsing Bitpanda trade: {e}")
            return None
