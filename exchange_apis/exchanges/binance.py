"""
Binance exchange implementation
"""
import time
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from urllib.parse import urlencode

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import create_signature_hmac_sha256, timestamp_to_ms, parse_decimal_safe
from ..core.pair_parsing import BinancePairParser
from ..core.asset_converters import asset_from_binance
from .base import BaseExchange


class BinanceExchange(BaseExchange):
    """Binance exchange implementation"""

    def __init__(self, name: str, api_key: str, api_secret: bytes, **kwargs):
        super().__init__(
            name=name,
            location=Location.BINANCE,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=10.0,  # 10 calls per second
            base_url="https://api.binance.com",
        )

    def validate_api_credentials(self) -> <PERSON><PERSON>[bool, str]:
        """Validate Binance API credentials"""
        try:
            response = self._make_authenticated_request('api/v3/account')
            if 'code' in response:
                return False, f"API Error: {response.get('msg', 'Unknown error')}"
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"

    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from Binance"""
        trades = []

        try:
            # Get all symbols first
            symbols_response = self._make_authenticated_request('api/v3/exchangeInfo')
            symbols = [s['symbol'] for s in symbols_response.get('symbols', [])]

            # Query trades for each symbol
            for symbol in symbols:
                try:
                    symbol_trades = self._query_symbol_trades(symbol, start_timestamp, end_timestamp)
                    trades.extend(symbol_trades)
                except Exception as e:
                    print(f"Error querying trades for {symbol}: {e}")
                    continue

        except Exception as e:
            print(f"Error querying Binance trades: {e}")

        return sorted(trades, key=lambda t: t.timestamp)

    def _query_symbol_trades(
        self,
        symbol: str,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trades for a specific symbol"""
        trades = []

        params = {
            'symbol': symbol,
            'startTime': timestamp_to_ms(start_timestamp),
            'endTime': timestamp_to_ms(end_timestamp),
            'limit': 1000,
        }

        response = self._make_authenticated_request(
            'api/v3/myTrades',
            method='GET',
            params=params
        )

        if isinstance(response, list):
            for trade_data in response:
                trade = self._parse_trade_data(trade_data)
                if trade:
                    trades.append(trade)

        return trades

    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate Binance authentication headers"""
        if not self.api_secret:
            return {}

        timestamp = str(int(time.time() * 1000))

        # Prepare query string
        query_params = params.copy() if params else {}
        query_params['timestamp'] = timestamp

        query_string = urlencode(query_params)

        # Create signature
        signature = create_signature_hmac_sha256(self.api_secret, query_string)
        query_string += f'&signature={signature}'

        return {
            'X-MBX-APIKEY': self.api_key,
        }

    def _parse_trade_data(self, raw_trade: Dict[str, Any]) -> Optional[Trade]:
        """Parse Binance trade data"""
        try:
            # Parse symbol into base and quote assets
            symbol = raw_trade.get('symbol', '')
            base_asset, quote_asset = self._parse_binance_symbol(symbol)

            # Parse trade type
            is_buyer = raw_trade.get('isBuyer', False)
            trade_type = TradeType.BUY if is_buyer else TradeType.SELL

            # Parse amounts and prices
            qty = parse_decimal_safe(raw_trade.get('qty', '0'))
            price = parse_decimal_safe(raw_trade.get('price', '0'))

            amount = AssetAmount(Decimal(str(qty)))
            rate = Price(Decimal(str(price)))

            # Parse fee
            commission = parse_decimal_safe(raw_trade.get('commission', '0'))
            fee = Fee(Decimal(str(commission))) if commission > 0 else None

            # Fee asset
            commission_asset = raw_trade.get('commissionAsset', '')
            fee_currency = Asset(commission_asset) if commission_asset and fee else None

            # Parse timestamp
            timestamp = Timestamp(int(raw_trade.get('time', 0)) // 1000)

            # Trade ID
            trade_id = str(raw_trade.get('id', ''))

            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=base_asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=amount,
                rate=rate,
                fee=fee,
                fee_currency=fee_currency,
                link=trade_id,
            )

        except Exception as e:
            print(f"Error parsing Binance trade: {e}")
            return None

    def _parse_binance_symbol(self, symbol: str) -> Tuple[Asset, Asset]:
        """Parse Binance symbol into base and quote assets"""
        # Common quote assets in order of preference
        quote_assets = ['USDT', 'BUSD', 'USDC', 'BTC', 'ETH', 'BNB', 'USD', 'EUR']

        for quote in quote_assets:
            if symbol.endswith(quote):
                base = symbol[:-len(quote)]
                return Asset(base), Asset(quote)

        # Fallback: assume last 3 characters are quote
        if len(symbol) > 3:
            base = symbol[:-3]
            quote = symbol[-3:]
            return Asset(base), Asset(quote)

        # Default fallback
        return Asset(symbol), Asset('UNKNOWN')
