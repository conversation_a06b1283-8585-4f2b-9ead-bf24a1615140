"""
BitMEX exchange implementation
"""
import time
import hashlib
import hmac
from typing import List, Dict, Any, Optional, <PERSON><PERSON>
from decimal import Decimal
from urllib.parse import urlencode
from http import HTTPStatus

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import parse_decimal_safe, make_request
from ..core.asset_converters import asset_from_exchange
from .base import BaseExchange


class BitmexExchange(BaseExchange):
    """BitMEX exchange implementation"""
    
    def __init__(self, name: str, api_key: str, api_secret: bytes, **kwargs):
        super().__init__(
            name=name,
            location=Location.BITMEX,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=1.0,  # 1 call per second
            base_url="https://www.bitmex.com",
        )
        
        # BitMEX specific settings
        self.api_version = "v1"
        
    def validate_api_credentials(self) -> <PERSON><PERSON>[bool, str]:
        """Validate BitMEX API credentials"""
        try:
            response = self._make_authenticated_request('api/v1/user')
            if 'error' in response:
                error_msg = response['error'].get('message', 'Unknown error')
                if 'Invalid API Key' in error_msg:
                    return False, 'Provided API Key is invalid'
                elif 'Signature not valid' in error_msg:
                    return False, 'Provided API Secret is invalid'
                return False, f"API Error: {error_msg}"
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"
    
    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from BitMEX"""
        trades = []
        
        try:
            # BitMEX primarily deals with margin trading, not spot trades
            # Query wallet history for realized PnL (margin positions)
            response = self._make_authenticated_request('api/v1/user/walletHistory', params={'currency': 'all'})
            
            if isinstance(response, list):
                for transaction in response:
                    if transaction.get('transactType') == 'RealisedPNL':
                        trade = self._parse_margin_position(transaction)
                        if trade and start_timestamp <= trade.timestamp <= end_timestamp:
                            trades.append(trade)
            
        except Exception as e:
            print(f"Error querying BitMEX trades: {e}")
        
        return sorted(trades, key=lambda t: t.timestamp)
    
    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate BitMEX authentication headers"""
        if not self.api_secret:
            return {}
        
        # BitMEX uses expiration timestamp (20 seconds from now)
        expires = int(time.time()) + 20
        
        # Create the request path
        api_path = f"/{endpoint.lstrip('/')}"
        if params:
            api_path += f"?{urlencode(params)}"
        
        # Create the message for signature
        # Format: VERB + PATH + EXPIRES + DATA
        body = ''
        if data:
            import json
            body = json.dumps(data)
        
        message = f"{method.upper()}{api_path}{expires}{body}"
        
        # Create signature
        signature = hmac.new(
            self.api_secret,
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return {
            'api-key': self.api_key,
            'api-expires': str(expires),
            'api-signature': signature,
        }
    
    def _parse_margin_position(self, raw_transaction: Dict[str, Any]) -> Optional[Trade]:
        """Parse BitMEX margin position (realized PnL) as a trade"""
        try:
            # BitMEX transaction format for realized PnL
            transaction_id = str(raw_transaction['transactID'])
            timestamp = self._parse_bitmex_timestamp(raw_transaction['transactTime'])
            
            # BitMEX asset conversion
            currency = raw_transaction['currency']
            asset = self._convert_bitmex_asset(currency)
            
            # Amount (profit/loss)
            amount_raw = parse_decimal_safe(str(raw_transaction['amount']))
            
            # For margin positions, we'll represent as a synthetic trade
            # Positive amount = profit (sell), negative = loss (buy)
            trade_type = TradeType.SELL if amount_raw > 0 else TradeType.BUY
            amount = abs(amount_raw)
            
            # Fee
            fee_amount = parse_decimal_safe(str(raw_transaction.get('fee', '0')))
            
            # Use BTC as the quote asset (BitMEX is primarily BTC-based)
            quote_asset = asset_from_exchange('BTC', self.location)
            
            # For synthetic rate, use 1:1 if we don't have better data
            rate = Decimal('1.0')
            
            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=AssetAmount(Decimal(str(amount))),
                rate=Price(rate),
                fee=Fee(Decimal(str(fee_amount))) if fee_amount > 0 else None,
                fee_currency=quote_asset,
                link=transaction_id,
            )
            
        except Exception as e:
            print(f"Error parsing BitMEX margin position: {e}")
            return None
    
    def _convert_bitmex_asset(self, symbol: str) -> Asset:
        """Convert BitMEX asset symbols"""
        # BitMEX specific conversions
        bitmex_map = {
            'XBt': 'BTC',  # BitMEX uses XBt for Bitcoin
            'Gwei': 'ETH',  # BitMEX uses Gwei for Ethereum
        }
        
        standard_symbol = bitmex_map.get(symbol, symbol.upper())
        return asset_from_exchange(standard_symbol, self.location)
    
    def _parse_bitmex_timestamp(self, timestamp_str: str) -> Timestamp:
        """Parse BitMEX timestamp string"""
        try:
            # BitMEX uses ISO 8601 format
            from datetime import datetime
            dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            return Timestamp(int(dt.timestamp()))
        except Exception:
            # Fallback to current time if parsing fails
            return Timestamp(int(time.time()))
