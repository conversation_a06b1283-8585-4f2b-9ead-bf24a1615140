"""
Bybit exchange implementation
"""
import time
import hashlib
import hmac
import urllib.parse
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from urllib.parse import urlencode
from http import HTTPStatus

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import parse_decimal_safe, make_request
from ..core.asset_converters import asset_from_exchange
from .base import BaseExchange

# RECEIVE_WINDOW specifies how long an HTTP request is valid (in ms)
RECEIVE_WINDOW = '10000'


class BybitExchange(BaseExchange):
    """Bybit exchange implementation"""
    
    def __init__(self, name: str, api_key: str, api_secret: bytes, **kwargs):
        super().__init__(
            name=name,
            location=Location.BYBIT,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=1.0,  # 1 call per second
            base_url="https://api.bybit.com",
        )
        
        # Bybit specific settings
        self.api_version = "v5"
        self.is_unified_account = False
        self.four_letter_assets = {'USDT', 'USDC', 'USDE'}  # Known quote assets
        
    def validate_api_credentials(self) -> Tuple[bool, str]:
        """Validate Bybit API credentials"""
        try:
            response = self._make_authenticated_request('v5/user/query-api')
            if response.get('retCode') != 0:
                return False, f"API Error: {response.get('retMsg', 'Unknown error')}"
            
            # Check if it's a unified account
            result = response.get('result', {})
            self.is_unified_account = result.get('uta') == 1
            
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"
    
    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from Bybit"""
        trades = []
        
        try:
            # Bybit limits queries to 2 years for unified accounts, 180 days for classic
            max_days = 365 * 2 if self.is_unified_account else 180
            earliest_ts = int(time.time()) - (max_days * 24 * 60 * 60)
            
            if end_timestamp <= earliest_ts:
                return []  # Query out of range
            
            if start_timestamp <= earliest_ts:
                start_timestamp = Timestamp(earliest_ts + 300)  # 5 minutes safety margin
            
            # Query in weekly chunks (Bybit limitation)
            week_seconds = 7 * 24 * 60 * 60
            current_start = start_timestamp
            
            while current_start < end_timestamp:
                current_end = min(current_start + week_seconds, end_timestamp)
                
                # Convert to milliseconds
                start_ms = current_start * 1000
                end_ms = current_end * 1000
                
                params = {
                    'category': 'spot',
                    'startTime': str(start_ms),
                    'endTime': str(end_ms),
                    'limit': '50',
                }
                
                response = self._make_authenticated_request('v5/order/history', params=params)
                
                if response.get('retCode') == 0:
                    result = response.get('result', {})
                    orders = result.get('list', [])
                    
                    for order_data in orders:
                        if order_data.get('orderStatus') == 'Filled':
                            trade = self._parse_trade_data(order_data)
                            if trade:
                                trades.append(trade)
                
                current_start = current_end
            
        except Exception as e:
            print(f"Error querying Bybit trades: {e}")
        
        return sorted(trades, key=lambda t: t.timestamp)
    
    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate Bybit authentication headers"""
        if not self.api_secret:
            return {}
        
        # Bybit uses millisecond timestamps
        timestamp = str(int(time.time() * 1000))
        
        # Create parameter string for signature
        param_str = timestamp + self.api_key + RECEIVE_WINDOW
        
        if params:
            # Sort parameters for consistent signature
            sorted_params = dict(sorted(params.items()))
            param_str += '&'.join([
                f"{k}={urllib.parse.quote_plus(str(v))}"
                for k, v in sorted_params.items()
                if v is not None
            ])
        
        # Create signature
        signature = hmac.new(
            self.api_secret,
            param_str.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return {
            'Content-Type': 'application/json',
            'X-BAPI-SIGN-TYPE': '2',
            'X-BAPI-RECV-WINDOW': RECEIVE_WINDOW,
            'X-BAPI-API-KEY': self.api_key,
            'X-BAPI-TIMESTAMP': timestamp,
            'X-BAPI-SIGN': signature,
        }
    
    def _parse_trade_data(self, raw_trade: Dict[str, Any]) -> Optional[Trade]:
        """Parse Bybit trade data"""
        try:
            # Bybit trade format
            trade_id = str(raw_trade.get('orderLinkId', raw_trade.get('orderId', '')))
            timestamp = Timestamp(int(raw_trade['updatedTime']) // 1000)  # Convert from ms
            
            # Parse symbol
            symbol = raw_trade['symbol']
            base_asset, quote_asset = self._parse_bybit_symbol(symbol)
            
            # Trade details
            side = raw_trade['side']
            trade_type = TradeType.BUY if side.lower() == 'buy' else TradeType.SELL
            
            amount = parse_decimal_safe(str(raw_trade['qty']))
            
            # Use average price if available, otherwise use order price
            if raw_trade['orderType'] == 'Market':
                price = parse_decimal_safe(str(raw_trade.get('avgPrice', raw_trade.get('price', '0'))))
            else:
                price = parse_decimal_safe(str(raw_trade.get('price', '0')))
            
            fee_amount = parse_decimal_safe(str(raw_trade.get('cumExecFee', '0')))
            
            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=base_asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=AssetAmount(Decimal(str(amount))),
                rate=Price(Decimal(str(price))),
                fee=Fee(Decimal(str(fee_amount))) if fee_amount > 0 else None,
                fee_currency=quote_asset,  # Bybit fees are typically in quote currency
                link=trade_id,
            )
            
        except Exception as e:
            print(f"Error parsing Bybit trade: {e}")
            return None
    
    def _parse_bybit_symbol(self, symbol: str) -> Tuple[Asset, Asset]:
        """Parse Bybit trading symbol"""
        # Handle special Bybit symbols with leverage tokens
        if '2L' in symbol:
            parts = symbol.split('2L')
        elif '2S' in symbol:
            parts = symbol.split('2S')
        elif '3L' in symbol:
            parts = symbol.split('3L')
        elif '3S' in symbol:
            parts = symbol.split('3S')
        elif '2' in symbol:
            parts = symbol.split('2')
        else:
            parts = None
        
        if parts and len(parts) == 2:
            base_str = parts[0]
            quote_str = parts[1]
        elif len(symbol) >= 4 and symbol[-4:] in self.four_letter_assets:
            # Handle 4-letter quote assets (USDT, USDC, etc.)
            base_str = symbol[:-4]
            quote_str = symbol[-4:]
        else:
            # Default: assume last 3 characters are quote
            base_str = symbol[:-3]
            quote_str = symbol[-3:]
        
        # Convert to assets
        base_asset = asset_from_exchange(base_str.upper(), self.location)
        quote_asset = asset_from_exchange(quote_str.upper(), self.location)
        
        return base_asset, quote_asset
