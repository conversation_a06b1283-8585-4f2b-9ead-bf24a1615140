"""
Kraken exchange implementation
"""
import time
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from urllib.parse import urlencode

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import create_signature_base64, current_timestamp, parse_decimal_safe
from .base import BaseExchange


class KrakenExchange(BaseExchange):
    """Kraken exchange implementation"""
    
    def __init__(self, name: str, api_key: str, api_secret: bytes, **kwargs):
        super().__init__(
            name=name,
            location=Location.KRAKEN,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=1.0,  # 1 call per second
            base_url="https://api.kraken.com",
        )
        
        # Kraken asset mapping
        self.asset_map = {
            'XXBT': 'BTC',
            'XETH': 'ETH',
            'ZUSD': 'USD',
            'ZEUR': 'EUR',
            'USDT': 'USDT',
            'USDC': 'USDC',
        }
    
    def validate_api_credentials(self) -> Tuple[bool, str]:
        """Validate Kraken API credentials"""
        try:
            response = self._make_authenticated_request('0/private/Balance')
            if 'error' in response and response['error']:
                return False, f"API Error: {response['error']}"
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"
    
    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from Kraken"""
        trades = []
        
        try:
            # Query trades from Kraken API
            params = {
                'type': 'all',
                'start': int(start_timestamp),
                'end': int(end_timestamp),
            }
            
            response = self._make_authenticated_request(
                '0/private/TradesHistory',
                method='POST',
                data=params
            )
            
            if 'error' in response and response['error']:
                raise Exception(f"Kraken API error: {response['error']}")
            
            trades_data = response.get('result', {}).get('trades', {})
            
            for trade_id, trade_data in trades_data.items():
                trade = self._parse_trade_data(trade_data)
                if trade:
                    trade.link = trade_id  # Set Kraken trade ID
                    trades.append(trade)
            
        except Exception as e:
            print(f"Error querying Kraken trades: {e}")
        
        return trades
    
    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate Kraken authentication headers"""
        if not self.api_secret:
            return {}
        
        nonce = str(int(time.time() * 1000000))
        
        # Prepare data for signature
        if data:
            postdata = urlencode(data)
        else:
            postdata = ''
        
        postdata = f'nonce={nonce}&{postdata}' if postdata else f'nonce={nonce}'
        
        # Create signature
        encoded = (nonce + postdata).encode()
        message = endpoint.encode() + encoded
        signature = create_signature_base64(self.api_secret, message.decode())
        
        return {
            'API-Key': self.api_key,
            'API-Sign': signature,
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    
    def _parse_trade_data(self, raw_trade: Dict[str, Any]) -> Optional[Trade]:
        """Parse Kraken trade data"""
        try:
            # Parse pair
            pair = raw_trade.get('pair', '')
            base_asset, quote_asset = self._parse_kraken_pair(pair)
            
            # Parse trade type
            trade_type_str = raw_trade.get('type', '').lower()
            trade_type = TradeType.BUY if trade_type_str == 'buy' else TradeType.SELL
            
            # Parse amounts and prices
            amount = AssetAmount(Decimal(str(raw_trade.get('vol', '0'))))
            rate = Price(Decimal(str(raw_trade.get('price', '0'))))
            
            # Parse fee
            fee_amount = parse_decimal_safe(raw_trade.get('fee', '0'))
            fee = Fee(Decimal(str(fee_amount))) if fee_amount > 0 else None
            
            # Parse timestamp
            timestamp = Timestamp(int(float(raw_trade.get('time', '0'))))
            
            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=base_asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=amount,
                rate=rate,
                fee=fee,
                fee_currency=quote_asset if fee else None,
            )
            
        except Exception as e:
            print(f"Error parsing Kraken trade: {e}")
            return None
    
    def _parse_kraken_pair(self, pair: str) -> Tuple[Asset, Asset]:
        """Parse Kraken trading pair into base and quote assets"""
        # Simplified pair parsing - in reality this would be more complex
        # This is a basic implementation
        
        if len(pair) == 6:
            base_str = pair[:3]
            quote_str = pair[3:]
        elif len(pair) == 8:
            base_str = pair[:4]
            quote_str = pair[4:]
        else:
            # Try common patterns
            for quote in ['USD', 'EUR', 'BTC', 'ETH']:
                if pair.endswith(quote):
                    base_str = pair[:-len(quote)]
                    quote_str = quote
                    break
            else:
                # Default fallback
                base_str = pair[:3]
                quote_str = pair[3:]
        
        # Map Kraken asset names to standard names
        base_asset = Asset(self.asset_map.get(base_str, base_str))
        quote_asset = Asset(self.asset_map.get(quote_str, quote_str))
        
        return base_asset, quote_asset
