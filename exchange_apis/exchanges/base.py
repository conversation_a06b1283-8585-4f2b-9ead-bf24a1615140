"""
Base exchange interface
"""
from abc import ABC, abstractmethod
from typing import List, Tuple, Optional, Dict, Any
import logging

from ..core.types import A<PERSON><PERSON><PERSON>, ApiSecret, Location, Timestamp
from ..core.trade import Trade
from ..core.utils import RateLimiter, make_request, APIError

logger = logging.getLogger(__name__)


class BaseExchange(ABC):
    """
    Base class for all exchange implementations

    Provides common functionality for API authentication, rate limiting,
    and trade history querying.
    """

    def __init__(
        self,
        name: str,
        location: Location,
        api_key: ApiKey,
        api_secret: Optional[ApiSecret] = None,
        rate_limit: float = 1.0,  # calls per second
        base_url: str = "",
    ):
        self.name = name
        self.location = location
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = base_url
        self.rate_limit = rate_limit
        self.rate_limiter = RateLimiter(rate_limit)
        self.session_headers: Dict[str, str] = {}

        logger.info(f"Initialized {location} exchange: {name}")

    @abstractmethod
    def validate_api_credentials(self) -> Tuple[bool, str]:
        """
        Validate API credentials

        Returns:
            Tuple of (success: bool, message: str)
        """
        pass

    def validate_api_key(self) -> Tuple[bool, str]:
        """
        Alias for validate_api_credentials for compatibility with rotkehlchen

        Returns:
            Tuple of (success: bool, message: str)
        """
        return self.validate_api_credentials()

    @abstractmethod
    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """
        Query trade history from the exchange

        Args:
            start_timestamp: Start time for trade history
            end_timestamp: End time for trade history

        Returns:
            List of Trade objects
        """
        pass

    def _make_authenticated_request(
        self,
        endpoint: str,
        method: str = 'GET',
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Make authenticated API request

        This method should be overridden by subclasses to implement
        exchange-specific authentication.
        """
        self.rate_limiter.wait_if_needed()

        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = self.session_headers.copy()

        # Add authentication headers (to be implemented by subclasses)
        auth_headers = self._get_auth_headers(endpoint, method, params, data)
        headers.update(auth_headers)

        return make_request(url, method, params, data, headers)

    @abstractmethod
    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """
        Generate authentication headers for the request

        To be implemented by each exchange subclass.
        """
        pass

    def _parse_trade_data(self, raw_trade: Dict[str, Any]) -> Optional[Trade]:
        """
        Parse raw trade data from exchange API into Trade object

        To be implemented by each exchange subclass.
        """
        raise NotImplementedError("Subclasses must implement _parse_trade_data")

    def get_supported_pairs(self) -> List[str]:
        """
        Get list of supported trading pairs

        Optional method that can be overridden by subclasses.
        """
        return []

    def __str__(self) -> str:
        return f"{self.__class__.__name__}({self.name})"

    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(name='{self.name}', location={self.location})"
