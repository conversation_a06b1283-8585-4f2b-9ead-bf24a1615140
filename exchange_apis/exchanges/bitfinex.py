"""
Bitfinex exchange implementation
"""
import time
import hashlib
import hmac
from typing import List, Dict, Any, Optional, Tu<PERSON>
from decimal import Decimal
from http import HTTPStatus

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import parse_decimal_safe, make_request
from ..core.asset_converters import asset_from_exchange
from .base import BaseExchange


class BitfinexExchange(BaseExchange):
    """Bitfinex exchange implementation"""
    
    def __init__(self, name: str, api_key: str, api_secret: bytes, **kwargs):
        super().__init__(
            name=name,
            location=Location.BITFINEX,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=1.0,  # 1 call per second
            base_url="https://api.bitfinex.com",
        )
        
        # Bitfinex specific settings
        self.api_version = "v2"
        
    def validate_api_credentials(self) -> Tuple[bool, str]:
        """Validate Bitfinex API credentials"""
        try:
            response = self._make_authenticated_request('auth/r/wallets')
            if response.get('error'):
                return False, f"API Error: {response['error']}"
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"
    
    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from Bitfinex"""
        trades = []
        
        try:
            # Bitfinex uses milliseconds
            start_ms = start_timestamp * 1000
            end_ms = end_timestamp * 1000
            
            # Query trades with pagination
            limit = 2500  # Bitfinex max limit
            
            options = {
                'start': start_ms,
                'end': end_ms,
                'limit': limit,
                'sort': 1,  # ascending
            }
            
            response = self._make_authenticated_request('auth/r/trades/hist', params=options)
            
            if isinstance(response, list):
                for trade_data in response:
                    trade = self._parse_trade_data(trade_data)
                    if trade:
                        trades.append(trade)
            
        except Exception as e:
            print(f"Error querying Bitfinex trades: {e}")
        
        return sorted(trades, key=lambda t: t.timestamp)
    
    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate Bitfinex authentication headers"""
        if not self.api_secret:
            return {}
        
        # Bitfinex uses millisecond timestamps
        nonce = str(int(time.time() * 1000))
        
        # Create the message for signature
        api_path = f"/api/{self.api_version}/{endpoint.lstrip('/')}"
        message = f"{api_path}{nonce}"
        
        # Create signature
        signature = hmac.new(
            self.api_secret,
            message.encode('utf-8'),
            hashlib.sha384
        ).hexdigest()
        
        return {
            'Content-Type': 'application/json',
            'bfx-apikey': self.api_key,
            'bfx-nonce': nonce,
            'bfx-signature': signature,
        }
    
    def _parse_trade_data(self, raw_trade: List[Any]) -> Optional[Trade]:
        """Parse Bitfinex trade data"""
        try:
            # Bitfinex trade format:
            # [ID, PAIR, MTS_CREATE, ORDER_ID, EXEC_AMOUNT, EXEC_PRICE, ORDER_TYPE, ORDER_PRICE, MAKER, FEE, FEE_CURRENCY, ...]
            
            if len(raw_trade) < 11:
                return None
            
            trade_id = str(raw_trade[0])
            pair = raw_trade[1]
            timestamp = Timestamp(int(raw_trade[2] / 1000))  # Convert from ms
            amount = parse_decimal_safe(str(raw_trade[4]))
            price = parse_decimal_safe(str(raw_trade[5]))
            fee_amount = parse_decimal_safe(str(raw_trade[9]))
            fee_currency_symbol = raw_trade[10]
            
            # Determine trade type
            trade_type = TradeType.BUY if amount > 0 else TradeType.SELL
            amount = abs(amount)
            
            # Parse pair (e.g., "tBTCUSD", "tETHUSD")
            base_asset, quote_asset = self._parse_bitfinex_pair(pair)
            
            # Parse fee
            fee = Fee(Decimal(str(abs(fee_amount)))) if fee_amount != 0 else None
            fee_currency = asset_from_exchange(fee_currency_symbol, self.location) if fee else None
            
            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=base_asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=AssetAmount(Decimal(str(amount))),
                rate=Price(Decimal(str(price))),
                fee=fee,
                fee_currency=fee_currency,
                link=trade_id,
            )
            
        except Exception as e:
            print(f"Error parsing Bitfinex trade: {e}")
            return None
    
    def _parse_bitfinex_pair(self, pair: str) -> Tuple[Asset, Asset]:
        """Parse Bitfinex trading pair"""
        # Remove 't' prefix if present
        if pair.startswith('t'):
            pair = pair[1:]
        
        # Handle colon separator (e.g., "ETH:USD")
        if ':' in pair:
            base_str, quote_str = pair.split(':', 1)
        else:
            # Common Bitfinex pairs
            if pair.endswith('USD'):
                base_str = pair[:-3]
                quote_str = 'USD'
            elif pair.endswith('EUR'):
                base_str = pair[:-3]
                quote_str = 'EUR'
            elif pair.endswith('BTC'):
                base_str = pair[:-3]
                quote_str = 'BTC'
            elif pair.endswith('ETH'):
                base_str = pair[:-3]
                quote_str = 'ETH'
            elif pair.endswith('USDT'):
                base_str = pair[:-4]
                quote_str = 'USDT'
            else:
                # Fallback: assume last 3 characters are quote
                base_str = pair[:-3]
                quote_str = pair[-3:]
        
        # Convert to assets
        base_asset = asset_from_exchange(base_str, self.location)
        quote_asset = asset_from_exchange(quote_str, self.location)
        
        return base_asset, quote_asset
