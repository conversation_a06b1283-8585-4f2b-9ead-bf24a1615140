"""
Iconomi exchange implementation
"""
import time
import hashlib
import hmac
import base64
from typing import List, Dict, Any, Optional, Tu<PERSON>
from decimal import Decimal
from urllib.parse import urlencode
from http import HTTPStatus

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import parse_decimal_safe, make_request
from ..core.asset_converters import asset_from_exchange
from .base import BaseExchange


class IconomiExchange(BaseExchange):
    """Iconomi exchange implementation"""
    
    def __init__(self, name: str, api_key: str, api_secret: bytes, **kwargs):
        super().__init__(
            name=name,
            location=Location.ICONOMI,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=1.0,  # 1 call per second
            base_url="https://api.iconomi.com",
        )
        
        # Iconomi specific settings
        self.api_version = "v1"
        
    def validate_api_credentials(self) -> <PERSON><PERSON>[bool, str]:
        """Validate Iconomi API credentials"""
        try:
            response = self._make_authenticated_request('v1/user/balance')
            if 'message' in response:
                return False, f"API Error: {response['message']}"
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"
    
    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from Iconomi"""
        trades = []
        
        try:
            # Query all transactions with pagination
            page = 0
            all_transactions = []
            
            while True:
                params = {'pageNumber': str(page)}
                response = self._make_authenticated_request('v1/user/activity', params=params)
                
                transactions = response.get('transactions', [])
                if not transactions:
                    break
                
                all_transactions.extend(transactions)
                page += 1
            
            # Filter and parse trades
            for transaction in all_transactions:
                timestamp = Timestamp(transaction.get('timestamp', 0))
                
                if not (start_timestamp <= timestamp <= end_timestamp):
                    continue
                
                if transaction.get('type') in {'buy_asset', 'sell_asset'}:
                    trade = self._parse_trade_data(transaction)
                    if trade:
                        trades.append(trade)
            
        except Exception as e:
            print(f"Error querying Iconomi trades: {e}")
        
        return sorted(trades, key=lambda t: t.timestamp)
    
    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate Iconomi authentication headers"""
        if not self.api_secret:
            return {}
        
        # Iconomi uses millisecond timestamps
        timestamp = str(int(time.time() * 1000))
        
        # Create the request path (without query parameters)
        request_path = f"/v1/{endpoint.lstrip('/')}"
        
        # Create the message for signature
        # Format: TIMESTAMP + METHOD + PATH
        message = f"{timestamp}{method.upper()}{request_path}"
        
        # Create signature
        signature = base64.b64encode(
            hmac.new(
                self.api_secret,
                message.encode('utf-8'),
                hashlib.sha512
            ).digest()
        ).decode()
        
        return {
            'ICN-API-KEY': self.api_key,
            'ICN-SIGN': signature,
            'ICN-TIMESTAMP': timestamp,
            'Content-Type': 'application/json',
        }
    
    def _parse_trade_data(self, raw_trade: Dict[str, Any]) -> Optional[Trade]:
        """Parse Iconomi trade data"""
        try:
            # Iconomi trade format
            trade_id = str(raw_trade['transactionId'])
            timestamp = Timestamp(raw_trade['timestamp'])
            
            trade_type_str = raw_trade['type']
            
            if trade_type_str == 'buy_asset':
                trade_type = TradeType.BUY
                # When buying: target is what we get, source is what we pay
                base_symbol = raw_trade['target_ticker']
                quote_symbol = raw_trade['source_ticker']
                amount = parse_decimal_safe(str(raw_trade['target_amount']))
                quote_amount = parse_decimal_safe(str(raw_trade['source_amount']))
                
            elif trade_type_str == 'sell_asset':
                trade_type = TradeType.SELL
                # When selling: source is what we sell, target is what we get
                base_symbol = raw_trade['source_ticker']
                quote_symbol = raw_trade['target_ticker']
                amount = parse_decimal_safe(str(raw_trade['source_amount']))
                quote_amount = parse_decimal_safe(str(raw_trade['target_amount']))
                
            else:
                return None  # Skip non-trade transactions
            
            # Convert assets
            base_asset = asset_from_exchange(base_symbol, self.location)
            quote_asset = asset_from_exchange(quote_symbol, self.location)
            
            # Calculate rate
            rate = quote_amount / amount if amount > 0 else Decimal('0')
            
            # Fee
            fee_amount = parse_decimal_safe(str(raw_trade.get('fee_amount', '0')))
            fee_symbol = raw_trade.get('fee_ticker', '')
            fee_currency = asset_from_exchange(fee_symbol, self.location) if fee_symbol else None
            
            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=base_asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=AssetAmount(Decimal(str(amount))),
                rate=Price(Decimal(str(rate))),
                fee=Fee(Decimal(str(fee_amount))) if fee_amount > 0 else None,
                fee_currency=fee_currency,
                link=trade_id,
            )
            
        except Exception as e:
            print(f"Error parsing Iconomi trade: {e}")
            return None
