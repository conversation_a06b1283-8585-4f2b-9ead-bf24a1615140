"""
Gemini exchange implementation
"""
import time
import hashlib
import hmac
import json
from base64 import b64encode
from typing import List, Dict, Any, Optional, Tuple
from decimal import Decimal
from http import HTTPStatus

from ..core.types import Location, Timestamp, TradeType, AssetAmount, Price, Fee, Asset
from ..core.trade import Trade
from ..core.utils import parse_decimal_safe, make_request
from ..core.asset_converters import asset_from_exchange
from .base import BaseExchange


class GeminiExchange(BaseExchange):
    """Gemini exchange implementation"""
    
    def __init__(self, name: str, api_key: str, api_secret: bytes, **kwargs):
        super().__init__(
            name=name,
            location=Location.GEMINI,
            api_key=api_key,
            api_secret=api_secret,
            rate_limit=1.0,  # 1 call per second
            base_url="https://api.gemini.com",
        )
        
        # Gemini specific settings
        self.api_version = "v1"
        self.symbols = []
        
    def validate_api_credentials(self) -> Tuple[bool, str]:
        """Validate Gemini API credentials"""
        try:
            response = self._make_authenticated_request('roles')
            if 'isAuditor' not in response:
                return False, "API key needs 'Auditor' permission"
            if not response.get('isAuditor', False):
                return False, "API key needs 'Auditor' permission activated"
            return True, "Credentials valid"
        except Exception as e:
            return False, f"Validation failed: {str(e)}"
    
    def query_trade_history(
        self,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Query trade history from Gemini"""
        trades = []
        
        try:
            # First get available symbols
            if not self.symbols:
                self.symbols = self._get_symbols()
            
            # Query trades for each symbol
            for symbol in self.symbols:
                symbol_trades = self._get_trades_for_symbol(symbol, start_timestamp, end_timestamp)
                trades.extend(symbol_trades)
            
        except Exception as e:
            print(f"Error querying Gemini trades: {e}")
        
        return sorted(trades, key=lambda t: t.timestamp)
    
    def _get_symbols(self) -> List[str]:
        """Get available trading symbols from Gemini"""
        try:
            response = make_request(f"{self.base_url}/v1/symbols", method='GET')
            return response if isinstance(response, list) else []
        except Exception:
            return []
    
    def _get_trades_for_symbol(
        self,
        symbol: str,
        start_timestamp: Timestamp,
        end_timestamp: Timestamp,
    ) -> List[Trade]:
        """Get trades for a specific symbol"""
        trades = []
        
        try:
            # Gemini uses millisecond timestamps
            timestamp_ms = start_timestamp * 1000
            limit = 500  # Gemini max limit
            
            while True:
                options = {
                    'symbol': symbol,
                    'timestamp': timestamp_ms,
                    'limit_trades': limit,
                }
                
                response = self._make_authenticated_request('mytrades', params=options)
                
                if not isinstance(response, list) or len(response) == 0:
                    break
                
                batch_trades = []
                for trade_data in response:
                    trade = self._parse_trade_data(trade_data, symbol)
                    if trade and start_timestamp <= trade.timestamp <= end_timestamp:
                        batch_trades.append(trade)
                
                trades.extend(batch_trades)
                
                # Check if we got fewer results than requested (end of data)
                if len(response) < limit:
                    break
                
                # Update timestamp for next page (most recent first, so use first item)
                timestamp_ms = response[0]['timestampms'] + 1
                
                # Check if we've gone past the end timestamp
                if int(response[0]['timestampms'] / 1000) > end_timestamp:
                    break
            
        except Exception as e:
            print(f"Error querying Gemini trades for {symbol}: {e}")
        
        return trades
    
    def _get_auth_headers(
        self,
        endpoint: str,
        method: str,
        params: Optional[Dict[str, Any]],
        data: Optional[Dict[str, Any]],
    ) -> Dict[str, str]:
        """Generate Gemini authentication headers"""
        if not self.api_secret:
            return {}
        
        # Gemini uses millisecond timestamps
        timestamp = str(int(time.time() * 1000))
        
        # Create the payload
        api_path = f"/v1/{endpoint.lstrip('/')}"
        payload = {
            'request': api_path,
            'nonce': timestamp,
        }
        
        # Add parameters to payload
        if params:
            payload.update(params)
        
        # Encode payload
        encoded_payload = json.dumps(payload).encode()
        b64_payload = b64encode(encoded_payload)
        
        # Create signature
        signature = hmac.new(
            self.api_secret,
            b64_payload,
            hashlib.sha384
        ).hexdigest()
        
        return {
            'Content-Type': 'text/plain',
            'X-GEMINI-APIKEY': self.api_key,
            'X-GEMINI-PAYLOAD': b64_payload.decode(),
            'X-GEMINI-SIGNATURE': signature,
            'Cache-Control': 'no-cache',
            'Content-Length': '0',
        }
    
    def _parse_trade_data(self, raw_trade: Dict[str, Any], symbol: str) -> Optional[Trade]:
        """Parse Gemini trade data"""
        try:
            # Gemini trade format
            trade_id = str(raw_trade['tid'])
            timestamp = Timestamp(int(raw_trade['timestamp']))
            
            # Parse pair from symbol
            base_asset, quote_asset = self._parse_gemini_symbol(symbol)
            
            # Trade details
            trade_type = TradeType.BUY if raw_trade['type'] == 'buy' else TradeType.SELL
            amount = parse_decimal_safe(str(raw_trade['amount']))
            price = parse_decimal_safe(str(raw_trade['price']))
            fee_amount = parse_decimal_safe(str(raw_trade['fee_amount']))
            fee_currency_symbol = raw_trade['fee_currency']
            
            # Parse fee
            fee = Fee(Decimal(str(fee_amount))) if fee_amount > 0 else None
            fee_currency = asset_from_exchange(fee_currency_symbol, self.location) if fee else None
            
            return Trade(
                timestamp=timestamp,
                location=self.location,
                base_asset=base_asset,
                quote_asset=quote_asset,
                trade_type=trade_type,
                amount=AssetAmount(Decimal(str(amount))),
                rate=Price(Decimal(str(price))),
                fee=fee,
                fee_currency=fee_currency,
                link=trade_id,
            )
            
        except Exception as e:
            print(f"Error parsing Gemini trade: {e}")
            return None
    
    def _parse_gemini_symbol(self, symbol: str) -> Tuple[Asset, Asset]:
        """Parse Gemini trading symbol"""
        # Gemini symbols are like "btcusd", "ethusd", "ethbtc"
        symbol = symbol.lower()
        
        # Common Gemini pairs
        if symbol.endswith('usd'):
            base_str = symbol[:-3]
            quote_str = 'USD'
        elif symbol.endswith('eur'):
            base_str = symbol[:-3]
            quote_str = 'EUR'
        elif symbol.endswith('gbp'):
            base_str = symbol[:-3]
            quote_str = 'GBP'
        elif symbol.endswith('btc'):
            base_str = symbol[:-3]
            quote_str = 'BTC'
        elif symbol.endswith('eth'):
            base_str = symbol[:-3]
            quote_str = 'ETH'
        else:
            # Fallback: assume last 3 characters are quote
            base_str = symbol[:-3]
            quote_str = symbol[-3:]
        
        # Convert to assets
        base_asset = asset_from_exchange(base_str.upper(), self.location)
        quote_asset = asset_from_exchange(quote_str.upper(), self.location)
        
        return base_asset, quote_asset
