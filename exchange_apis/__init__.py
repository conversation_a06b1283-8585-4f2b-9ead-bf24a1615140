"""
Minimal Exchange API Library for Trade Export

This package provides a lightweight interface to various cryptocurrency exchange APIs
focused specifically on trade history export functionality.

Key Features:
- Unified Trade object across all exchanges
- Minimal dependencies
- Support for major exchanges (Kraken, Binance, Coinbase, etc.)
- Rate limiting and error handling
"""

from .core.types import Location, TradeType, Timestamp, Asset
from .core.trade import Trade
from .core.asset_converters import asset_from_kraken, asset_from_binance, asset_from_coinbase, asset_from_exchange
from .core.pair_parsing import UniversalPairParser
from .exchanges.base import BaseExchange

# Import all exchange implementations
from .exchanges.kraken import KrakenExchange
from .exchanges.binance import BinanceExchange
from .exchanges.coinbase import CoinbaseExchange
from .exchanges.bitfinex import BitfinexExchange
from .exchanges.bitstamp import BitstampExchange
from .exchanges.kucoin import KuCoinExchange
from .exchanges.gemini import GeminiExchange
from .exchanges.okx import OKXExchange
from .exchanges.poloniex import PoloniexExchange

__version__ = "1.0.0"
__all__ = [
    # Core types and classes
    "Location",
    "TradeType",
    "Timestamp",
    "Asset",
    "Trade",
    "BaseExchange",

    # Asset converters
    "asset_from_kraken",
    "asset_from_binance",
    "asset_from_coinbase",
    "asset_from_exchange",

    # Pair parsing
    "UniversalPairParser",

    # Exchange implementations
    "KrakenExchange",
    "BinanceExchange",
    "CoinbaseExchange",
    "BitfinexExchange",
    "BitstampExchange",
    "KuCoinExchange",
    "GeminiExchange",
    "OKXExchange",
    "PoloniexExchange",
]
