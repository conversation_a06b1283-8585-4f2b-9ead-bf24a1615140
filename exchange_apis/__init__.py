"""
Minimal Exchange API Library for Trade Export

This package provides a lightweight interface to various cryptocurrency exchange APIs
focused specifically on trade history export functionality.

Key Features:
- Unified Trade object across all exchanges
- Minimal dependencies
- Support for major exchanges (Kraken, Binance, Coinbase, etc.)
- Rate limiting and error handling
"""

from .core.types import Location, TradeType, Timestamp, Asset
from .core.trade import Trade
from .core.asset_converters import asset_from_kraken, asset_from_binance, asset_from_coinbase
from .core.pair_parsing import UniversalPairParser
from .exchanges.base import BaseExchange

__version__ = "1.0.0"
__all__ = [
    "Location",
    "TradeType",
    "Timestamp",
    "Asset",
    "Trade",
    "BaseExchange",
    "asset_from_kraken",
    "asset_from_binance",
    "asset_from_coinbase",
    "UniversalPairParser",
]
