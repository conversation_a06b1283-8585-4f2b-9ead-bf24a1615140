"""
Trade data structure - simplified version without heavy dependencies
"""
from dataclasses import dataclass
from typing import Optional, Dict, Any
import hashlib
import json

from .types import Timestamp, Location, Asset, TradeType, AssetAmount, Price, Fee


@dataclass
class Trade:
    """
    Represents a cryptocurrency trade
    
    Simplified version of the original Trade class with minimal dependencies.
    """
    timestamp: Timestamp
    location: Location
    base_asset: Asset
    quote_asset: Asset
    trade_type: TradeType
    amount: AssetAmount  # Amount bought if buy, amount sold if sell (excluding fees)
    rate: Price  # Price per unit
    fee: Optional[Fee] = None
    fee_currency: Optional[Asset] = None
    link: Optional[str] = None  # Exchange trade ID or external link
    notes: Optional[str] = None

    @property
    def identifier(self) -> str:
        """Generate a unique identifier for the trade"""
        data = (
            str(self.location),
            str(self.timestamp),
            str(self.trade_type),
            self.base_asset.identifier,
            self.quote_asset.identifier,
            str(self.amount),
            str(self.rate),
            self.link or ''
        )
        return hashlib.sha256(''.join(data).encode()).hexdigest()[:16]

    def to_dict(self) -> Dict[str, Any]:
        """Convert trade to dictionary"""
        return {
            'timestamp': int(self.timestamp),
            'location': str(self.location),
            'base_asset': self.base_asset.identifier,
            'quote_asset': self.quote_asset.identifier,
            'trade_type': str(self.trade_type),
            'amount': str(self.amount),
            'rate': str(self.rate),
            'fee': str(self.fee) if self.fee else None,
            'fee_currency': self.fee_currency.identifier if self.fee_currency else None,
            'link': self.link,
            'notes': self.notes,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Trade':
        """Create trade from dictionary"""
        from decimal import Decimal
        
        return cls(
            timestamp=Timestamp(data['timestamp']),
            location=Location.from_string(data['location']),
            base_asset=Asset(data['base_asset']),
            quote_asset=Asset(data['quote_asset']),
            trade_type=TradeType.from_string(data['trade_type']),
            amount=AssetAmount(Decimal(str(data['amount']))),
            rate=Price(Decimal(str(data['rate']))),
            fee=Fee(Decimal(str(data['fee']))) if data.get('fee') else None,
            fee_currency=Asset(data['fee_currency']) if data.get('fee_currency') else None,
            link=data.get('link'),
            notes=data.get('notes'),
        )

    def to_json(self) -> str:
        """Convert trade to JSON string"""
        return json.dumps(self.to_dict(), indent=2)

    @classmethod
    def from_json(cls, json_str: str) -> 'Trade':
        """Create trade from JSON string"""
        return cls.from_dict(json.loads(json_str))

    def __str__(self) -> str:
        from datetime import datetime
        dt = datetime.fromtimestamp(self.timestamp)
        return (
            f"Trade({self.trade_type} {self.amount} {self.base_asset.symbol} "
            f"for {self.quote_asset.symbol} at {self.rate} on {self.location} "
            f"at {dt.strftime('%Y-%m-%d %H:%M:%S')})"
        )
