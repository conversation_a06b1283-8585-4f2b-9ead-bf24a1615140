"""
Asset converters for different exchanges - extracted from rot<PERSON><PERSON>chen
"""
from typing import Dict, Set
from .types import Asset, Location, UnknownAsset, UnsupportedAsset


# Kraken asset mappings and converters
KRAKEN_ASSET_MAP = {
    # Standard mappings
    'XXBT': 'BTC',
    'XBT': 'BTC', 
    'XETH': 'ETH',
    'ZUSD': 'USD',
    'ZEUR': 'EUR',
    'ZGBP': 'GBP',
    'ZCAD': 'CAD',
    'ZJPY': 'JPY',
    'ZKRW': 'KRW',
    'ZCHF': 'CHF',
    'ZAUD': 'AUD',
    'XDG': 'DOGE',
    'SETH': 'ETH2',
    'FLOWH': 'FLOW',
    
    # Direct mappings (no change needed)
    'ETH': 'ETH',
    'EUR': 'EUR', 
    'USD': 'USD',
    'GBP': 'GBP',
    'CAD': 'CAD',
    'JPY': 'JPY',
    'KRW': 'KRW',
    'CHF': 'CHF',
    'AUD': 'AUD',
    'BTC': 'BTC',
    'DOGE': 'DOGE',
    
    # Common cryptocurrencies
    'ADA': 'ADA',
    'DOT': 'DOT',
    'LINK': 'LINK',
    'LTC': 'LTC',
    'XRP': 'XRP',
    'BCH': 'BCH',
    'EOS': 'EOS',
    'XLM': 'XLM',
    'TRX': 'TRX',
    'USDT': 'USDT',
    'USDC': 'USDC',
    'DAI': 'DAI',
    'BUSD': 'BUSD',
    'BNB': 'BNB',
    'MATIC': 'MATIC',
    'AVAX': 'AVAX',
    'SOL': 'SOL',
    'ATOM': 'ATOM',
    'ALGO': 'ALGO',
    'VET': 'VET',
    'FIL': 'FIL',
    'THETA': 'THETA',
    'ICP': 'ICP',
    'AAVE': 'AAVE',
    'UNI': 'UNI',
    'SUSHI': 'SUSHI',
    'COMP': 'COMP',
    'MKR': 'MKR',
    'SNX': 'SNX',
    'YFI': 'YFI',
    'CRV': 'CRV',
    '1INCH': '1INCH',
    'BAL': 'BAL',
    'RUNE': 'RUNE',
    'LUNA': 'LUNA',
    'NEAR': 'NEAR',
    'FTM': 'FTM',
    'MANA': 'MANA',
    'SAND': 'SAND',
    'AXS': 'AXS',
    'ENJ': 'ENJ',
    'CHZ': 'CHZ',
    'BAT': 'BAT',
    'ZRX': 'ZRX',
    'OMG': 'OMG',
    'LRC': 'LRC',
    'GRT': 'GRT',
    'STORJ': 'STORJ',
    'SKL': 'SKL',
    'ANKR': 'ANKR',
    'NKN': 'NKN',
    'OXT': 'OXT',
    'REP': 'REP',
    'REPV2': 'REPV2',
    'KNC': 'KNC',
    'MLN': 'MLN',
    'ANT': 'ANT',
    'BAND': 'BAND',
    'KAVA': 'KAVA',
    'KEEP': 'KEEP',
    'NU': 'NU',
    'OGN': 'OGN',
    'REN': 'REN',
    'TBTC': 'TBTC',
    'CTSI': 'CTSI',
    'TRU': 'TRU',
    'BADGER': 'BADGER',
    'API3': 'API3',
    'PERP': 'PERP',
    'RARI': 'RARI',
    'ALPHA': 'ALPHA',
    'AUDIO': 'AUDIO',
    'MASK': 'MASK',
    'LPT': 'LPT',
    'BOND': 'BOND',
    'RLC': 'RLC',
    'OCEAN': 'OCEAN',
    'BNT': 'BNT',
    'CRO': 'CRO',
    'HOT': 'HOT',
    'QTUM': 'QTUM',
    'ICX': 'ICX',
    'ZIL': 'ZIL',
    'ONT': 'ONT',
    'IOST': 'IOST',
    'ZEC': 'ZEC',
    'XMR': 'XMR',
    'DASH': 'DASH',
    'ETC': 'ETC',
    'NEO': 'NEO',
    'WAVES': 'WAVES',
    'LSK': 'LSK',
    'NANO': 'NANO',
    'SC': 'SC',
    'DGB': 'DGB',
    'DCR': 'DCR',
    'XTZ': 'XTZ',
}

# Binance renamed assets
RENAMED_BINANCE_ASSETS = {
    'BCHSV': 'BSV',
    'BCHABC': 'BCH',
    'BCHSV': 'BSV',
    'YOYOW': 'YOYO',
}

# Staking/special asset suffixes for Kraken
KRAKEN_STAKING_SUFFIXES = {'.S', '.M', '.P', '.F', '.B'}


def asset_from_kraken(kraken_name: str) -> Asset:
    """
    Convert Kraken asset name to standard Asset
    
    May raise:
    - UnknownAsset
    - UnsupportedAsset
    """
    if not isinstance(kraken_name, str):
        raise ValueError(f'Got non-string type {type(kraken_name)} for kraken asset')

    original_name = kraken_name
    
    # Handle staking/allocated assets
    if any(kraken_name.endswith(suffix) for suffix in KRAKEN_STAKING_SUFFIXES):
        kraken_name = kraken_name[:-2]
        
        if kraken_name != 'ETH2':
            # Remove bonded number days assets
            while kraken_name and kraken_name[-1].isdigit():
                kraken_name = kraken_name[:-1]
    
    # Remove .HOLD suffix
    kraken_name = kraken_name.removesuffix('.HOLD')
    
    # Map to standard asset name
    if kraken_name in KRAKEN_ASSET_MAP:
        standard_name = KRAKEN_ASSET_MAP[kraken_name]
        return Asset(standard_name, standard_name, standard_name)
    
    # If not found in map, use as-is but warn
    return Asset(kraken_name, kraken_name, kraken_name)


def asset_from_binance(binance_name: str) -> Asset:
    """
    Convert Binance asset name to standard Asset
    
    May raise:
    - UnknownAsset
    - UnsupportedAsset
    """
    if not isinstance(binance_name, str):
        raise ValueError(f'Got non-string type {type(binance_name)} for binance asset')
    
    # Handle renamed assets
    if binance_name in RENAMED_BINANCE_ASSETS:
        standard_name = RENAMED_BINANCE_ASSETS[binance_name]
        return Asset(standard_name, standard_name, standard_name)
    
    # For most assets, use as-is
    return Asset(binance_name, binance_name, binance_name)


def asset_from_coinbase(coinbase_name: str) -> Asset:
    """
    Convert Coinbase asset name to standard Asset
    """
    if not isinstance(coinbase_name, str):
        raise ValueError(f'Got non-string type {type(coinbase_name)} for coinbase asset')
    
    # Coinbase generally uses standard names
    return Asset(coinbase_name, coinbase_name, coinbase_name)


def asset_from_kucoin(kucoin_name: str) -> Asset:
    """
    Convert KuCoin asset name to standard Asset
    """
    if not isinstance(kucoin_name, str):
        raise ValueError(f'Got non-string type {type(kucoin_name)} for kucoin asset')
    
    # KuCoin generally uses standard names
    return Asset(kucoin_name, kucoin_name, kucoin_name)


def asset_from_exchange(exchange_name: str, location: Location) -> Asset:
    """
    Convert exchange-specific asset name to standard Asset based on location
    """
    if location == Location.KRAKEN:
        return asset_from_kraken(exchange_name)
    elif location == Location.BINANCE or location == Location.BINANCEUS:
        return asset_from_binance(exchange_name)
    elif location == Location.COINBASE or location == Location.COINBASEPRO:
        return asset_from_coinbase(exchange_name)
    elif location == Location.KUCOIN:
        return asset_from_kucoin(exchange_name)
    else:
        # Default: use as-is
        return Asset(exchange_name, exchange_name, exchange_name)
