"""
Comprehensive pair parsing for different exchanges
"""
import re
from typing import Tuple, Dict, List, Optional, Set
from dataclasses import dataclass

from .types import Asset, Location, UnprocessableTradePair
from .asset_converters import asset_from_kraken, asset_from_binance, asset_from_coinbase


@dataclass
class TradingPair:
    """Represents a trading pair with base and quote assets"""
    symbol: str
    base_asset: Asset
    quote_asset: Asset
    location: Location


class KrakenPairParser:
    """Kraken pair parsing logic"""

    def __init__(self):
        # Common quote assets in Kraken, ordered by preference for parsing
        self.quote_assets = [
            'USD', 'EUR', 'GBP', 'CAD', 'JPY', 'CHF', 'AUD', 'KRW',  # Fiat
            'ZUSD', 'ZEUR', 'ZGBP', 'ZCAD', 'ZJPY', 'ZCHF', 'ZAUD', 'ZKRW',  # Kraken fiat
            'BTC', 'ETH', 'USDT', 'USDC', 'DAI',  # Crypto
            'XXBT', 'XETH',  # Kraken crypto
        ]

        # Known Kraken pairs cache (populated from API)
        self.known_pairs: Dict[str, Tuple[str, str]] = {}

        # Flag to indicate if we should prefer API data over fallback parsing
        self.use_api_data = True

    def parse_pair(self, pair: str) -> Tuple[Asset, Asset]:
        """
        Parse Kraken trading pair into base and quote assets

        Kraken pairs can be complex:
        - XXBTZUSD (BTC/USD)
        - XETHZEUR (ETH/EUR)
        - ADAUSD (ADA/USD)
        - DOTUSD (DOT/USD)
        - etc.
        """
        if not pair:
            raise UnprocessableTradePair(pair)

        # Check if we have this pair in API data (highest priority)
        if self.use_api_data and pair in self.known_pairs:
            base_str, quote_str = self.known_pairs[pair]
            # API data already contains standard names, so don't use keep_kraken_format
            return Asset(base_str), Asset(quote_str)

        # Fallback to advanced parsing for unknown pairs
        base_str, quote_str = self._parse_kraken_pair_advanced(pair)

        try:
            # For fallback parsing, convert Kraken names to standard names
            base_asset = asset_from_kraken(base_str)
            quote_asset = asset_from_kraken(quote_str)
            return base_asset, quote_asset
        except Exception:
            raise UnprocessableTradePair(f"Cannot parse Kraken pair: {pair}")

    def _parse_kraken_pair_advanced(self, pair: str) -> Tuple[str, str]:
        """Advanced Kraken pair parsing with special case handling"""

        # Handle special dot notation (e.g., ETH2.SETH)
        if '.' in pair:
            parts = pair.split('.')
            if len(parts) == 2:
                return parts[0], parts[1]

        # Handle complex Kraken pairs with known patterns
        complex_pairs = self._handle_complex_kraken_pairs(pair)
        if complex_pairs:
            return complex_pairs

        # Try to parse using quote asset patterns first
        for quote in self.quote_assets:
            if pair.endswith(quote):
                base_str = pair[:-len(quote)]
                if base_str:  # Make sure we have a base asset
                    return base_str, quote

        # Special patterns for Kraken's complex naming
        # Pattern: XXBTZUSD -> XXBT + ZUSD
        if len(pair) == 8:
            # Try 4-4 split first
            base_str = pair[:4]
            quote_str = pair[4:]
            if self._is_valid_kraken_asset_pair(base_str, quote_str):
                return base_str, quote_str

            # Try other splits for 8-character pairs
            for split_pos in [3, 5]:
                base_str = pair[:split_pos]
                quote_str = pair[split_pos:]
                if self._is_valid_kraken_asset_pair(base_str, quote_str):
                    return base_str, quote_str

        # Pattern: XETHZEUR -> XETH + ZEUR (7 chars)
        elif len(pair) == 7:
            # Try 4-3 split first (common for XETH + EUR)
            base_str = pair[:4]
            quote_str = pair[4:]
            if self._is_valid_kraken_asset_pair(base_str, quote_str):
                return base_str, quote_str

            # Try 3-4 split
            base_str = pair[:3]
            quote_str = pair[3:]
            if self._is_valid_kraken_asset_pair(base_str, quote_str):
                return base_str, quote_str

        # Standard 6-character pairs
        elif len(pair) == 6:
            # Try 3-3 split
            base_str = pair[:3]
            quote_str = pair[3:]
            if self._is_valid_kraken_asset_pair(base_str, quote_str):
                return base_str, quote_str

        # For other lengths, try different splits
        else:
            for split_pos in range(2, len(pair) - 1):
                base_str = pair[:split_pos]
                quote_str = pair[split_pos:]
                if self._is_valid_kraken_asset_pair(base_str, quote_str):
                    return base_str, quote_str

        # Fallback: assume last 3-4 characters are quote
        for quote_len in [4, 3]:
            if len(pair) > quote_len:
                base_str = pair[:-quote_len]
                quote_str = pair[-quote_len:]
                if base_str and quote_str:
                    return base_str, quote_str

        # Final fallback
        if len(pair) >= 4:
            return pair[:-3], pair[-3:]
        else:
            return pair, ""

    def _is_valid_kraken_asset_pair(self, base: str, quote: str) -> bool:
        """Check if base/quote combination looks like valid Kraken assets"""
        if not base or not quote:
            return False

        # Check if quote is a known quote asset
        if quote in self.quote_assets:
            return True

        # Check if both are known Kraken assets
        from .asset_converters import KRAKEN_ASSET_MAP
        if base in KRAKEN_ASSET_MAP and quote in KRAKEN_ASSET_MAP:
            return True

        # Check patterns - Kraken assets often start with X or Z
        if quote.startswith(('Z', 'X')) and len(quote) >= 3:
            return True

        # Common crypto quote patterns
        if quote in ['BTC', 'ETH', 'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF']:
            return True

        return False

    def _handle_complex_kraken_pairs(self, pair: str) -> Optional[Tuple[str, str]]:
        """Handle complex Kraken pairs with specific patterns"""

        # Known complex patterns that need special handling
        complex_patterns = {
            # XXBT patterns
            'XXBTZUSD': ('XXBT', 'ZUSD'),
            'XXBTZEUR': ('XXBT', 'ZEUR'),
            'XXBTZGBP': ('XXBT', 'ZGBP'),
            'XXBTZJPY': ('XXBT', 'ZJPY'),
            'XXBTZCAD': ('XXBT', 'ZCAD'),

            # XETH patterns
            'XETHZUSD': ('XETH', 'ZUSD'),
            'XETHZEUR': ('XETH', 'ZEUR'),
            'XETHZGBP': ('XETH', 'ZGBP'),
            'XETHZJPY': ('XETH', 'ZJPY'),
            'XETHZCAD': ('XETH', 'ZCAD'),
            'XETHXXBT': ('XETH', 'XXBT'),

            # XLTC patterns
            'XLTCXXBT': ('XLTC', 'XXBT'),
            'XLTCZEUR': ('XLTC', 'ZEUR'),
            'XLTCZUSD': ('XLTC', 'ZUSD'),
            'XLTCZJPY': ('XLTC', 'ZJPY'),

            # XMLN patterns
            'XMLNXETH': ('XMLN', 'XETH'),
            'XMLNXXBT': ('XMLN', 'XXBT'),
            'XMLNZEUR': ('XMLN', 'ZEUR'),
            'XMLNZUSD': ('XMLN', 'ZUSD'),

            # XREP patterns
            'XREPXXBT': ('XREP', 'XXBT'),
            'XREPZEUR': ('XREP', 'ZEUR'),
            'XREPZUSD': ('XREP', 'ZUSD'),

            # XXRP patterns
            'XXRPXXBT': ('XXRP', 'XXBT'),
            'XXRPZCAD': ('XXRP', 'ZCAD'),
            'XXRPZEUR': ('XXRP', 'ZEUR'),
            'XXRPZJPY': ('XXRP', 'ZJPY'),
            'XXRPZUSD': ('XXRP', 'ZUSD'),

            # XXLM patterns
            'XXLMXXBT': ('XXLM', 'XXBT'),
            'XXLMZEUR': ('XXLM', 'ZEUR'),
            'XXLMZGBP': ('XXLM', 'ZGBP'),
            'XXLMZUSD': ('XXLM', 'ZUSD'),

            # XXMR patterns
            'XXMRXXBT': ('XXMR', 'XXBT'),
            'XXMRZEUR': ('XXMR', 'ZEUR'),
            'XXMRZUSD': ('XXMR', 'ZUSD'),

            # XXDG patterns
            'XXDGXXBT': ('XXDG', 'XXBT'),

            # XZEC patterns
            'XZECXXBT': ('XZEC', 'XXBT'),
            'XZECZEUR': ('XZEC', 'ZEUR'),
            'XZECZUSD': ('XZEC', 'ZUSD'),

            # XETC patterns
            'XETCXETH': ('XETC', 'XETH'),
            'XETCXXBT': ('XETC', 'XXBT'),
            'XETCZEUR': ('XETC', 'ZEUR'),
            'XETCZUSD': ('XETC', 'ZUSD'),

            # Z currency patterns
            'ZEURZUSD': ('ZEUR', 'ZUSD'),
            'ZGBPZUSD': ('ZGBP', 'ZUSD'),
            'ZUSDZCAD': ('ZUSD', 'ZCAD'),
            'ZUSDZJPY': ('ZUSD', 'ZJPY'),
            'USDTZUSD': ('USDT', 'ZUSD'),

            # Special cases
            'USDAED': ('USD', 'AED'),
            'XBTAED': ('XBT', 'AED'),
            'ETHAED': ('ETH', 'AED'),

            # Short asset + XBT patterns
            'ICXXBT': ('ICX', 'XBT'),
            'SNXXBT': ('SNX', 'XBT'),
            'TRXXBT': ('TRX', 'XBT'),
            'ZRXXBT': ('ZRX', 'XBT'),
            'KSMDOT': ('KSM', 'DOT'),
        }

        if pair in complex_patterns:
            return complex_patterns[pair]

        return None


class BinancePairParser:
    """Binance pair parsing logic"""

    def __init__(self):
        # Common quote assets in Binance, ordered by preference
        self.quote_assets = [
            'USDT', 'BUSD', 'USDC', 'TUSD', 'PAX', 'USDP',  # Stablecoins
            'BTC', 'ETH', 'BNB',  # Major crypto
            'USD', 'EUR', 'GBP', 'AUD', 'TRY', 'RUB', 'UAH',  # Fiat
            'BIDR', 'BKRW', 'IDRT', 'NGN', 'PLN', 'RON', 'ZAR',  # Other fiat
        ]

        # Cache for known pairs
        self.known_pairs: Dict[str, Tuple[str, str]] = {}

    def parse_symbol(self, symbol: str) -> Tuple[Asset, Asset]:
        """
        Parse Binance symbol into base and quote assets

        Binance symbols are generally straightforward:
        - BTCUSDT (BTC/USDT)
        - ETHBTC (ETH/BTC)
        - ADAEUR (ADA/EUR)
        """
        if not symbol:
            raise UnprocessableTradePair(symbol)

        # Check cache first
        if symbol in self.known_pairs:
            base_str, quote_str = self.known_pairs[symbol]
            return asset_from_binance(base_str), asset_from_binance(quote_str)

        # Try to match against known quote assets
        for quote in self.quote_assets:
            if symbol.endswith(quote):
                base_str = symbol[:-len(quote)]
                if base_str:  # Make sure we have a base asset
                    try:
                        base_asset = asset_from_binance(base_str)
                        quote_asset = asset_from_binance(quote)
                        # Cache the result
                        self.known_pairs[symbol] = (base_str, quote)
                        return base_asset, quote_asset
                    except Exception:
                        continue

        # Fallback: assume last 3-4 characters are quote
        for quote_len in [4, 3]:
            if len(symbol) > quote_len:
                base_str = symbol[:-quote_len]
                quote_str = symbol[-quote_len:]
                try:
                    base_asset = asset_from_binance(base_str)
                    quote_asset = asset_from_binance(quote_str)
                    self.known_pairs[symbol] = (base_str, quote_str)
                    return base_asset, quote_asset
                except Exception:
                    continue

        raise UnprocessableTradePair(f"Cannot parse Binance symbol: {symbol}")


class CoinbasePairParser:
    """Coinbase pair parsing logic"""

    def parse_pair(self, pair: str) -> Tuple[Asset, Asset]:
        """
        Parse Coinbase pair

        Coinbase pairs are usually separated by '-':
        - BTC-USD
        - ETH-EUR
        - ADA-BTC
        """
        if not pair:
            raise UnprocessableTradePair(pair)

        if '-' in pair:
            parts = pair.split('-')
            if len(parts) == 2:
                base_str, quote_str = parts
                return asset_from_coinbase(base_str), asset_from_coinbase(quote_str)

        raise UnprocessableTradePair(f"Cannot parse Coinbase pair: {pair}")


class KuCoinPairParser:
    """KuCoin pair parsing logic"""

    def parse_pair(self, pair: str) -> Tuple[Asset, Asset]:
        """
        Parse KuCoin pair

        KuCoin pairs are usually separated by '-':
        - BTC-USDT
        - ETH-BTC
        - ADA-USD
        """
        if not pair:
            raise UnprocessableTradePair(pair)

        if '-' in pair:
            parts = pair.split('-')
            if len(parts) == 2:
                base_str, quote_str = parts
                return asset_from_kucoin(base_str), asset_from_kucoin(quote_str)

        raise UnprocessableTradePair(f"Cannot parse KuCoin pair: {pair}")


class UniversalPairParser:
    """Universal pair parser that handles all exchanges"""

    def __init__(self):
        self.kraken_parser = KrakenPairParser()
        self.binance_parser = BinancePairParser()
        self.coinbase_parser = CoinbasePairParser()
        self.kucoin_parser = KuCoinPairParser()

    def parse_pair(self, pair: str, location: Location) -> Tuple[Asset, Asset]:
        """Parse trading pair based on exchange location"""
        if location == Location.KRAKEN:
            return self.kraken_parser.parse_pair(pair)
        elif location in (Location.BINANCE, Location.BINANCEUS):
            return self.binance_parser.parse_symbol(pair)
        elif location in (Location.COINBASE, Location.COINBASEPRO):
            return self.coinbase_parser.parse_pair(pair)
        elif location == Location.KUCOIN:
            return self.kucoin_parser.parse_pair(pair)
        else:
            # Generic fallback
            return self._generic_parse(pair)

    def _generic_parse(self, pair: str) -> Tuple[Asset, Asset]:
        """Generic pair parsing for unknown exchanges"""
        # Try common separators
        for separator in ['-', '_', '/']:
            if separator in pair:
                parts = pair.split(separator)
                if len(parts) == 2:
                    return Asset(parts[0]), Asset(parts[1])

        # Try common quote assets
        common_quotes = ['USDT', 'USD', 'BTC', 'ETH', 'EUR']
        for quote in common_quotes:
            if pair.endswith(quote):
                base = pair[:-len(quote)]
                if base:
                    return Asset(base), Asset(quote)

        raise UnprocessableTradePair(f"Cannot parse pair: {pair}")
