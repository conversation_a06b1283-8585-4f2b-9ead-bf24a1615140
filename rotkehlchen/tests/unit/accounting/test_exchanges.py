from collections import defaultdict
from contextlib import suppress
from unittest.mock import patch

import pytest

from rotkehlchen.accounting.mixins.event import AccountingEventType
from rotkehlchen.api.server import APIServer
from rotkehlchen.assets.asset import Asset
from rotkehlchen.chain.evm.accounting.structures import BaseEventSettings
from rotkehlchen.constants import ONE, ZERO
from rotkehlchen.constants.assets import A_BTC, A_ETH, A_EUR, A_KSM, A_LINK, A_USDT
from rotkehlchen.db.accounting_rules import DBAccountingRules
from rotkehlchen.db.history_events import DBHistoryEvents
from rotkehlchen.errors.misc import InputError
from rotkehlchen.exchanges.data_structures import MarginPosition, Trade
from rotkehlchen.fval import FVal
from rotkehlchen.history.events.structures.asset_movement import AssetMovement
from rotkehlchen.history.events.structures.base import HistoryEvent
from rotkehlchen.history.events.structures.types import HistoryEventSubType, HistoryEventType
from rotkehlchen.tests.utils.accounting import accounting_create_and_process_history
from rotkehlchen.tests.utils.exchanges import mock_normal_coinbase_query
from rotkehlchen.tests.utils.history import prices
from rotkehlchen.types import (
    AssetAmount,
    Fee,
    Location,
    Price,
    Timestamp,
    TimestampMS,
    TradeType,
)


@pytest.mark.parametrize('have_decoders', [True])
@pytest.mark.parametrize(
    'added_exchanges',
    [(Location.COINBASE,)],
)
@pytest.mark.parametrize('ethereum_accounts', [[]])
@pytest.mark.parametrize('mocked_price_queries', [prices])
@pytest.mark.parametrize('initialize_accounting_rules', [True])
def test_account_for_coinbase_income_expense(rotkehlchen_api_server_with_exchanges):
    """
    Test that history events generated by coinbase are taken into account in accounting
    """
    rotki = rotkehlchen_api_server_with_exchanges.rest_api.rotkehlchen
    coinbase = rotki.exchange_manager.get_exchange('coinbase', Location.COINBASE)

    with patch.object(coinbase.session, 'get', side_effect=mock_normal_coinbase_query):
        report, events = accounting_create_and_process_history(rotki=rotki, start_ts=0, end_ts=**********)  # noqa: E501

    expected_total_actions = 8
    assert report['total_actions'] == expected_total_actions
    events_map = defaultdict(int)
    for event in events:
        events_map[event.event_type] += 1

    assert events_map[AccountingEventType.TRADE] == 4
    assert events_map[AccountingEventType.ASSET_MOVEMENT] == 1
    assert events_map[AccountingEventType.TRANSACTION_EVENT] == 2
    assert sum(events_map.values()) == 7  # processed events


@pytest.mark.parametrize('have_decoders', [True])
@pytest.mark.parametrize('default_mock_price_value', [FVal(1.5)])
@pytest.mark.parametrize('ethereum_accounts', [[]])
@pytest.mark.parametrize('added_exchanges', [(Location.COINBASE,)])
def test_exchanges_removed_api_keys(rotkehlchen_api_server_with_exchanges: APIServer):
    """
    Test that if actions made on an exchange are stored in the DB but the API keys were removed
    then the actions are still taken into account in accounting.
    """
    rotki = rotkehlchen_api_server_with_exchanges.rest_api.rotkehlchen
    with rotki.data.db.user_write() as write_cursor:
        rotki.data.db.add_trades(write_cursor, trades=[Trade(
            timestamp=Timestamp(**********),
            amount=AssetAmount(ONE),
            base_asset=A_ETH,
            quote_asset=A_BTC,
            trade_type=TradeType.BUY,
            rate=Price(FVal(1.5)),
            location=Location.COINBASE,
        ), Trade(
            timestamp=Timestamp(**********),
            amount=AssetAmount(FVal(7)),
            base_asset=A_USDT,
            quote_asset=A_LINK,
            trade_type=TradeType.SELL,
            rate=Price(FVal(7)),
            location=Location.EXTERNAL,
        )])
        DBHistoryEvents(rotki.data.db).add_history_events(
            write_cursor=write_cursor,
            history=[AssetMovement(
                timestamp=TimestampMS(*************),
                location=Location.COINBASE,
                event_type=HistoryEventType.DEPOSIT,
                asset=A_BTC,
                amount=ONE,
            ), AssetMovement(
                timestamp=TimestampMS(*************),
                location=Location.COINBASE,
                event_type=HistoryEventType.DEPOSIT,
                asset=A_BTC,
                amount=FVal(0.00001),
                is_fee=True,
            )],
        )
        rotki.data.db.add_margin_positions(write_cursor, margin_positions=[MarginPosition(
            location=Location.COINBASE,
            open_time=Timestamp(**********),
            close_time=Timestamp(**********),
            profit_loss=AssetAmount(ONE),
            pl_currency=A_BTC,
            fee=Fee(ZERO),
            fee_currency=A_BTC,
            link='no link',
            notes='no notes',
        )])

    rotki.exchange_manager.delete_exchange(name='coinbase', location=Location.COINBASE)
    _, events = accounting_create_and_process_history(rotki=rotki, start_ts=Timestamp(0), end_ts=Timestamp(**********))  # noqa: E501
    assert len(events) == 6
    event1 = events[0]
    assert event1.event_type == AccountingEventType.TRADE
    assert event1.location == Location.COINBASE
    assert event1.taxable_amount == FVal(1.5)
    assert event1.asset == A_BTC

    event2 = events[1]
    assert event2.event_type == AccountingEventType.TRADE
    assert event2.location == Location.COINBASE
    assert event2.free_amount == ONE
    assert event2.asset == A_ETH

    event3 = events[2]
    assert event3.event_type == AccountingEventType.TRADE
    assert event3.location == Location.EXTERNAL
    assert event3.taxable_amount == FVal(7)
    assert event3.asset == A_USDT

    event4 = events[3]
    assert event4.event_type == AccountingEventType.TRADE
    assert event4.location == Location.EXTERNAL
    assert event4.free_amount == FVal(49)
    assert event4.asset == A_LINK

    event5 = events[4]
    assert event5.event_type == AccountingEventType.MARGIN_POSITION
    assert event5.location == Location.COINBASE
    assert event5.free_amount == ZERO
    assert event5.taxable_amount == ONE
    assert event5.asset == A_BTC

    event6 = events[5]
    assert event6.event_type == AccountingEventType.ASSET_MOVEMENT
    assert event6.location == Location.COINBASE
    assert event6.free_amount == ZERO
    assert event6.taxable_amount == FVal(0.00001)
    assert event6.asset == A_BTC


@pytest.mark.parametrize('have_decoders', [True])
@pytest.mark.parametrize('default_mock_price_value', [FVal(1.5)])
@pytest.mark.parametrize('ethereum_accounts', [[]])
@pytest.mark.parametrize('added_exchanges', [[]])
def test_process_kraken_events(rotkehlchen_api_server_with_exchanges: APIServer):
    """
    Test that trades and assets movements get ignored from kraken events but any other
    type of event gets processed
    """
    rotki = rotkehlchen_api_server_with_exchanges.rest_api.rotkehlchen
    with suppress(InputError):
        DBAccountingRules(rotki.data.db).add_accounting_rule(
            event_type=HistoryEventType.ADJUSTMENT,
            event_subtype=HistoryEventSubType.RECEIVE,
            counterparty=None,
            rule=BaseEventSettings(
                taxable=True,
                count_entire_amount_spend=True,
                count_cost_basis_pnl=True,
            ),
            links={},
        )

    history = [
        HistoryEvent(  # should be ignored
            identifier=7,
            event_identifier='event_0',
            sequence_index=1,
            timestamp=TimestampMS(*************),
            location=Location.KRAKEN,
            asset=A_ETH,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.SPEND,
            amount=FVal('0.0025'),
            notes='SPEND 0.0025 ETH in kraken',
        ), HistoryEvent(  # should be ignored as part of the trade
            identifier=8,
            event_identifier='event_0',
            sequence_index=2,
            timestamp=TimestampMS(*************),
            location=Location.KRAKEN,
            asset=A_EUR,
            event_type=HistoryEventType.TRADE,
            event_subtype=HistoryEventSubType.RECEIVE,
            amount=FVal('0.0025'),
            notes='Receive 0.0025 EUR in kraken',
        ), HistoryEvent(  # should be processed
            identifier=9,
            event_identifier='event_1',
            sequence_index=1,
            timestamp=TimestampMS(1675913100000),
            location=Location.KRAKEN,
            asset=A_KSM,
            event_type=HistoryEventType.STAKING,
            event_subtype=HistoryEventSubType.REWARD,
            amount=FVal('0.1932938'),
            notes='Staking reward of 0.1932938 ETH in kraken',
        ), HistoryEvent(  # should be processed
            identifier=10,
            event_identifier='event_2',
            sequence_index=1,
            timestamp=TimestampMS(1675914100000),
            location=Location.KRAKEN,
            asset=Asset('ETHW'),
            event_type=HistoryEventType.ADJUSTMENT,
            event_subtype=HistoryEventSubType.RECEIVE,
            amount=FVal('0.1932938'),
            notes='Receive forked asset',
        ), HistoryEvent(  # should be ignored
            identifier=11,
            event_identifier='event_3',
            sequence_index=1,
            timestamp=TimestampMS(*************),
            location=Location.KRAKEN,
            asset=Asset('BTC'),
            event_type=HistoryEventType.DEPOSIT,
            event_subtype=HistoryEventSubType.DEPOSIT_ASSET,
            amount=ONE,
            notes='Deposit BTC brrrrr',
        )]

    with rotki.data.db.user_write() as write_cursor:
        DBHistoryEvents(rotki.data.db).add_history_events(write_cursor, history=history)

    _, events = accounting_create_and_process_history(
        rotki=rotki,
        start_ts=Timestamp(0),
        end_ts=Timestamp(**********),
    )
    assert len(events) == 2
    assert events[0].event_type == AccountingEventType.STAKING
    assert events[0].asset == A_KSM
    assert events[1].asset == Asset('ETHW')
    assert events[1].notes == 'Receive forked asset'
