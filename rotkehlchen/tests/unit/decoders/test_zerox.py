from typing import TYPE_CHECKING, Final

import pytest

from rotkehlchen.assets.asset import Asset
from rotkehlchen.chain.ethereum.modules.zerox.constants import ZEROX_ROUTER
from rotkehlchen.chain.evm.decoding.constants import CPT_GAS
from rotkehlchen.chain.evm.decoding.cowswap.constants import CPT_COWSWAP
from rotkehlchen.chain.evm.decoding.zerox.constants import CPT_ZEROX
from rotkehlchen.chain.evm.types import string_to_evm_address
from rotkehlchen.chain.optimism.modules.zerox.constants import ZEROX_ROUTER as OP_ZEROX_ROUTER
from rotkehlchen.constants.assets import A_ETH, A_OP, A_POLYGON_POS_MATIC, A_SNX, A_USDC, A_USDT
from rotkehlchen.constants.resolver import strethaddress_to_identifier
from rotkehlchen.fval import FVal
from rotkehlchen.history.events.structures.evm_event import EvmEvent
from rotkehlchen.history.events.structures.types import HistoryEventSubType, HistoryEventType
from rotkehlchen.tests.unit.decoders.test_aerodrome import A_AERO
from rotkehlchen.tests.unit.decoders.test_metamask import A_OPTIMISM_USDC
from rotkehlchen.tests.utils.constants import A_OPTIMISM_USDT
from rotkehlchen.tests.utils.ethereum import get_decoded_events_of_transaction
from rotkehlchen.types import Location, TimestampMS, deserialize_evm_tx_hash

if TYPE_CHECKING:
    from rotkehlchen.chain.ethereum.node_inquirer import EthereumInquirer


A_AI: Final = Asset(strethaddress_to_identifier('******************************************'))
A_DRGN: Final = Asset(strethaddress_to_identifier('******************************************'))
A_MYRIA: Final = Asset(strethaddress_to_identifier('******************************************'))
A_LFG: Final = Asset(strethaddress_to_identifier('******************************************'))
A_BANANA: Final = Asset(strethaddress_to_identifier('******************************************'))
A_OXN: Final = Asset(strethaddress_to_identifier('******************************************'))
A_RAINI: Final = Asset(strethaddress_to_identifier('******************************************'))
A_PRIME: Final = Asset(strethaddress_to_identifier('******************************************'))
A_TSUKA: Final = Asset(strethaddress_to_identifier('******************************************'))
A_GF: Final = Asset(strethaddress_to_identifier('******************************************'))
A_SHIB: Final = Asset(strethaddress_to_identifier('******************************************'))
A_LMWR: Final = Asset(strethaddress_to_identifier('******************************************'))
A_IXS: Final = Asset(strethaddress_to_identifier('******************************************'))
A_RLB: Final = Asset(strethaddress_to_identifier('******************************************'))
A_DERC: Final = Asset(strethaddress_to_identifier('******************************************'))
A_POLYGON_POS_USDT: Final = Asset('eip155:137/erc20:******************************************')
A_ARBITRUM_USDC: Final = Asset('eip155:42161/erc20:******************************************')
A_WSTETH: Final = Asset('eip155:42161/erc20:******************************************')
A_BASE_USDC: Final = Asset('eip155:8453/erc20:******************************************')
A_USDE: Final = Asset(strethaddress_to_identifier('******************************************'))
A_PENDLE: Final = Asset(strethaddress_to_identifier('******************************************'))
A_BULL: Final = Asset('eip155:137/erc20:******************************************')


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_sell_to_uniswap(ethereum_inquirer, ethereum_accounts):
    tx_hash = deserialize_evm_tx_hash('0xb9827174e182a1b8df3507d13c5cedccdc974c4edd5d66f59355f7e9758b9006')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '12.***************', '646.553802069266414457', '0.018008266883477871'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ETH,
        amount=FVal(gas_fees),
        location_label=ethereum_accounts[0],
        notes=f'Burn {gas_fees} ETH for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_BANANA,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} BANANA via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_OXN,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} OxN as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_sell_eth_for_token_to_uniswap_v3(ethereum_inquirer, ethereum_accounts):
    tx_hash = deserialize_evm_tx_hash('0x5b7719016f7d7d3d8ed9d4d86afd0e0079551d0a7795f70f01764ce5eaa44478')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '0.*************', '85192.182824334037015387', '0.007717607607009392'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ETH,
        amount=FVal(gas_fees),
        location_label=ethereum_accounts[0],
        notes=f'Burn {gas_fees} ETH for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_ETH,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} ETH via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_MYRIA,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} MYRIA as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_sell_token_for_eth_to_uniswap_v3(ethereum_inquirer, ethereum_accounts):
    tx_hash = deserialize_evm_tx_hash('0x68416e19252c678cdf67ae9b7adff742d78f95cea3c3f0582d3dc930340e5bdf')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '150000', '1.446309922122136822', '0.013066559749685724'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ETH,
        amount=FVal(gas_fees),
        location_label=ethereum_accounts[0],
        notes=f'Burn {gas_fees} ETH for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_LFG,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} @LFG via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_ETH,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} ETH as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_sell_token_for_token_to_uniswap_v3(ethereum_inquirer, ethereum_accounts):
    tx_hash = deserialize_evm_tx_hash('0x6efe8a18de9ca3183bdb319be445f1b0b9041c0e8208fa04a58ee276b54574dd')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '********.499107021225491254', '6627.784156933620641174', '0.011785737692958302'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ETH,
        amount=FVal(gas_fees),
        location_label=ethereum_accounts[0],
        notes=f'Burn {gas_fees} ETH for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_AI,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} AI via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_DRGN,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} DRGN as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_multiplex_batch_sell_eth_for_token(ethereum_inquirer, ethereum_accounts):
    tx_hash = deserialize_evm_tx_hash('0xa151bc4f1c69591598386eaa65761cefd706cbfe0a1a340d8856dbfe2c3bd8c5')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '1.***************', '155577.466855002838240404', '0.****************'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ETH,
        amount=FVal(gas_fees),
        location_label=ethereum_accounts[0],
        notes=f'Burn {gas_fees} ETH for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_ETH,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} ETH via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_RAINI,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} RAINI as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_multiplex_batch_sell_token_for_eth(ethereum_inquirer, ethereum_accounts):
    tx_hash = deserialize_evm_tx_hash('0xf9b40a3bbbd92fe72099cff45564e099782fc9b0b4bd40c2d87484b43735b3b1')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '13438.664993496960137988', '2.669421884825430502', '0.*****************'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ETH,
        amount=FVal(gas_fees),
        location_label=ethereum_accounts[0],
        notes=f'Burn {gas_fees} ETH for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_LMWR,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} LMWR via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_ETH,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} ETH as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_multiplex_batch_sell_token_for_token(ethereum_inquirer, ethereum_accounts):
    tx_hash = deserialize_evm_tx_hash('0x0f422be6e6904700181c3effb0600a8ed7e1616e70e6587d383b29290d6a7c1d')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '125.156732374288853661', '43405.********', '0.018050672959219548'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ETH,
        amount=FVal(gas_fees),
        location_label=ethereum_accounts[0],
        notes=f'Burn {gas_fees} ETH for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_PRIME,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} PRIME via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_TSUKA,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} TSUKA as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_multiplex_multihop_sell_token_for_token(ethereum_inquirer, ethereum_accounts):
    tx_hash = deserialize_evm_tx_hash('0xceccb105d312df00105eca2560b8da4cd0e791bb0f0da4cebeb17ca46abf2ce4')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '64666', '143469.489576604990799103', '0.*****************'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ETH,
        amount=FVal(gas_fees),
        location_label=ethereum_accounts[0],
        notes=f'Burn {gas_fees} ETH for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_USDT,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} USDT via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_GF,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} GF as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_0x415565b0_eth_to_token(ethereum_inquirer, ethereum_accounts):
    """Test ETH to Token swaps done through 0x415565b0 method ID via the 0x protocol router contract."""  # noqa: E501
    tx_hash = deserialize_evm_tx_hash('0x56dd5341b27b744e3ef3a2f356a6db48cb811397495a4cb9e8924c8232ef9abc')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '2.****************', '*********.664559041869620658', '0.016733414171811015'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ETH,
        amount=FVal(gas_fees),
        location_label=ethereum_accounts[0],
        notes=f'Burn {gas_fees} ETH for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_ETH,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} ETH via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_SHIB,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} SHIB as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_0x415565b0_token_to_eth(ethereum_inquirer, ethereum_accounts):
    """Test Token to ETH swaps done through 0x415565b0 method ID via the 0x protocol router contract."""  # noqa: E501
    tx_hash = deserialize_evm_tx_hash('0xc4bab35f7499def296e9ccb08eebd8933ad8c37ff2701f2750027600f9050c55')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '5000', '1.450387601635590055', '0.*****************'
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ETH,
        amount=FVal(gas_fees),
        location_label=ethereum_accounts[0],
        notes=f'Burn {gas_fees} ETH for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_USDC,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} USDC via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_ETH,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} ETH as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_0x415565b0_token_to_token(ethereum_inquirer, ethereum_accounts):
    """Test Token to Token swaps done through 0x415565b0 method ID via the 0x protocol router contract."""  # noqa: E501
    tx_hash = deserialize_evm_tx_hash('0x29bd536ecd4cacec3495b02f6375ab7465be64fff015916484746cd18da7a37d')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '1496', '1480.467089', '0.014804818364279386'
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ETH,
        amount=FVal(gas_fees),
        location_label=ethereum_accounts[0],
        notes=f'Burn {gas_fees} ETH for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_USDT,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} USDT via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_USDC,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} USDC as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_execute_meta_transaction_v2(ethereum_inquirer, ethereum_accounts):
    """Test meta transaction swaps done via the 0x protocol router contract."""
    tx_hash = deserialize_evm_tx_hash('0xb77f8e36a86517928f890296a263cafafa48ef01c6cc424a838b59b4401bf314')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, meta_tx_fees = '1405.596892', '4910.533168813496285354', '58.171192'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_USDT,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} USDT via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_IXS,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} IXS as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_USDT,
        amount=FVal(meta_tx_fees),
        location_label=ethereum_accounts[0],
        notes=f'Spend {meta_tx_fees} USDT as a 0x protocol fee',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_execute_meta_transaction_v2_multiplex(ethereum_inquirer, ethereum_accounts):
    """Test meta transaction multiplex swaps done via the 0x protocol router contract."""
    tx_hash = deserialize_evm_tx_hash('0x48f48d62b9829152c5963716acaed198320595859093cfc8a117742287f5a5eb')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, meta_tx_fees = '49934.597014', '352963.071479518181477885', '65.402986'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_USDT,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} USDT via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_RLB,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} RLB as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_USDT,
        amount=FVal(meta_tx_fees),
        location_label=ethereum_accounts[0],
        notes=f'Spend {meta_tx_fees} USDT as a 0x protocol fee',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_execute_meta_transaction_v2_flash(ethereum_inquirer, ethereum_accounts):
    """Test meta transaction swaps done via the 0x protocol using its flash contract."""
    tx_hash = deserialize_evm_tx_hash('0xf0be288974b275768725e7322c66d4086bd9b70bac4427af394966d333c0c807')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, meta_tx_fees = '10659.465069', '25162.301091908076364354', '60.028487'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_USDT,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} USDT via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_DERC,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} DERC as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_USDT,
        amount=FVal(meta_tx_fees),
        location_label=ethereum_accounts[0],
        notes=f'Spend {meta_tx_fees} USDT as a 0x protocol fee',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('polygon_pos_accounts', [['******************************************']])
def test_swap_on_polygon_pos(polygon_pos_inquirer, polygon_pos_accounts):
    tx_hash = deserialize_evm_tx_hash('0x8e7c52d519d1ca0d1dfd8c8c21a2d2c34574e2bdada0ae7faafd49c1ddb8e8a6')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(
        evm_inquirer=polygon_pos_inquirer,
        tx_hash=tx_hash,
    )
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '0.130848158787696777', '0.146129', '0.*****************'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.POLYGON_POS,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_POLYGON_POS_MATIC,
        amount=FVal(gas_fees),
        location_label=polygon_pos_accounts[0],
        notes=f'Burn {gas_fees} POL for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.POLYGON_POS,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_POLYGON_POS_MATIC,
        amount=FVal(swap_amount),
        location_label=polygon_pos_accounts[0],
        notes=f'Swap {swap_amount} POL via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.POLYGON_POS,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_POLYGON_POS_USDT,
        amount=FVal(received_amount),
        location_label=polygon_pos_accounts[0],
        notes=f'Receive {received_amount} USDT as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('arbitrum_one_accounts', [['0xf06cc31757760CC9B8235C868ED90789f9c1E883']])
def test_swap_arbitrum_one(arbitrum_one_inquirer, arbitrum_one_accounts):
    tx_hash = deserialize_evm_tx_hash('0x355c18ab70fb5d17098b6bc8fd527ce00f0b25c8220d6fe29522a1fb64b711bc')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(
        evm_inquirer=arbitrum_one_inquirer,
        tx_hash=tx_hash,
    )
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, meta_tx_fees = '49920.273922', '11.*****************', '79.726078'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ARBITRUM_ONE,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_ARBITRUM_USDC,
        amount=FVal(swap_amount),
        location_label=arbitrum_one_accounts[0],
        notes=f'Swap {swap_amount} USDC via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ARBITRUM_ONE,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_WSTETH,
        amount=FVal(received_amount),
        location_label=arbitrum_one_accounts[0],
        notes=f'Receive {received_amount} wstETH as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ARBITRUM_ONE,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ARBITRUM_USDC,
        amount=FVal(meta_tx_fees),
        location_label=arbitrum_one_accounts[0],
        notes=f'Spend {meta_tx_fees} USDC as a 0x protocol fee',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('optimism_accounts', [['******************************************']])
def test_swap_optimism(optimism_inquirer, optimism_accounts):
    tx_hash = deserialize_evm_tx_hash('0x6b2b2d8c0cf2e27bb9e6c8309fd9887a066f9b72139acfe13d7ca5c29ae6c0ff')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=optimism_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, meta_tx_fees = '1.181244', '1.180785', '0.818756'
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.OPTIMISM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_OPTIMISM_USDC,
        amount=FVal(swap_amount),
        location_label=optimism_accounts[0],
        notes=f'Swap {swap_amount} USDC via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=OP_ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.OPTIMISM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_OPTIMISM_USDT,
        amount=FVal(received_amount),
        location_label=optimism_accounts[0],
        notes=f'Receive {received_amount} USDT as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=OP_ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.OPTIMISM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_OPTIMISM_USDC,
        amount=FVal(meta_tx_fees),
        location_label=optimism_accounts[0],
        notes=f'Spend {meta_tx_fees} USDC as a 0x protocol fee',
        counterparty=CPT_ZEROX,
        address=OP_ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('base_accounts', [['0xF68D2BfCecd7895BBa05a7451Dd09A1749026454']])
def test_swap_base(base_inquirer, base_accounts):
    tx_hash = deserialize_evm_tx_hash('0x4a5eb8fac7ef1d6637ff1d54e67791e4a5a49effb141f30e5af90f5aba5d48a5')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=base_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, meta_tx_fees = '688.271588', '1726.*****************', '4.288419'
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.BASE,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_BASE_USDC,
        amount=FVal(swap_amount),
        location_label=base_accounts[0],
        notes=f'Swap {swap_amount} USDC via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.BASE,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_AERO,
        amount=FVal(received_amount),
        location_label=base_accounts[0],
        notes=f'Receive {received_amount} AERO as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.BASE,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_BASE_USDC,
        amount=FVal(meta_tx_fees),
        location_label=base_accounts[0],
        notes=f'Spend {meta_tx_fees} USDC as a 0x protocol fee',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_swap_on_pancakeswap(ethereum_inquirer, ethereum_accounts):
    tx_hash = deserialize_evm_tx_hash('0xcfb3b1587bb4d24a06d0f543493098ab285ae3763a489911da5bbea99bcb3499')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '10.***************', '36912.012249', '0.017837784148433769'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ETH,
        amount=FVal(gas_fees),
        location_label=ethereum_accounts[0],
        notes=f'Burn {gas_fees} ETH for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_ETH,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} ETH via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_USDT,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} USDT as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_swap_on_curve(ethereum_inquirer, ethereum_accounts):
    tx_hash = deserialize_evm_tx_hash('0x12d794e7ced93da02978aa9b46b59f27ceab49724fdb1b0c39963792af68fdf0')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '458480.413', '459177.562744', '0.*****************'
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ETH,
        amount=FVal(gas_fees),
        location_label=ethereum_accounts[0],
        notes=f'Burn {gas_fees} ETH for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_USDE,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} USDe via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_USDT,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} USDT as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_swap_on_sushiswap(ethereum_inquirer, ethereum_accounts):
    tx_hash = deserialize_evm_tx_hash('0x802f7d1c4b2f1b7ef48e5c3af92a3a166a91624b89e736f85b90df3dd7ce9d73')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '50', '68.557872437267381014', '0.007767959110971294'
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ETH,
        amount=FVal(gas_fees),
        location_label=ethereum_accounts[0],
        notes=f'Burn {gas_fees} ETH for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_SNX,
        amount=FVal(swap_amount),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} SNX via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_PENDLE,
        amount=FVal(received_amount),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} PENDLE as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr
@pytest.mark.parametrize('polygon_pos_accounts', [['******************************************']])
def test_swap_on_quickswap(polygon_pos_inquirer, polygon_pos_accounts):
    tx_hash = deserialize_evm_tx_hash('0x7b1bef89b8890e060787924a279e040ce8d50aedd35337747af6d56024ce269e')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(
        evm_inquirer=polygon_pos_inquirer,
        tx_hash=tx_hash,
    )
    timestamp = TimestampMS(*************)
    swap_amount, received_amount, gas_fees = '58.*************', '10313.421767180867447506', '0.***********'  # noqa: E501
    expected_events = [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.POLYGON_POS,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_POLYGON_POS_MATIC,
        amount=FVal(gas_fees),
        location_label=polygon_pos_accounts[0],
        notes=f'Burn {gas_fees} POL for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.POLYGON_POS,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_POLYGON_POS_MATIC,
        amount=FVal(swap_amount),
        location_label=polygon_pos_accounts[0],
        notes=f'Swap {swap_amount} POL via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.POLYGON_POS,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=A_BULL,
        amount=FVal(received_amount),
        location_label=polygon_pos_accounts[0],
        notes=f'Receive {received_amount} BULL as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=ZEROX_ROUTER,
    )]
    assert expected_events == events


@pytest.mark.vcr(filter_query_parameters=['apikey'])
@pytest.mark.parametrize('optimism_accounts', [['0x9531C059098e3d194fF87FebB587aB07B30B1306']])
def test_swap_optimism_with_return(optimism_inquirer, optimism_accounts):
    """Check that a swap with an amount returned is decoded correctly.
    Regression test for https://github.com/rotki/rotki/issues/9122
    """
    tx_hash = deserialize_evm_tx_hash('0x0745615163e01c8a446f2520d5fa008dd69f308f24bcc3d2fec2466c1a2bc25c')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=optimism_inquirer, tx_hash=tx_hash)
    user_address, timestamp, gas_amount, swap_amount, received_amount = optimism_accounts[0], TimestampMS(*************), '0.000527795236079617', '1813.728944336150781643', '5820.310306'  # noqa: E501
    assert events == [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=timestamp,
        location=Location.OPTIMISM,
        event_type=HistoryEventType.SPEND,
        event_subtype=HistoryEventSubType.FEE,
        asset=A_ETH,
        amount=FVal(gas_amount),
        location_label=user_address,
        notes=f'Burn {gas_amount} ETH for gas',
        counterparty=CPT_GAS,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.OPTIMISM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=A_OP,
        amount=FVal(swap_amount),
        location_label=user_address,
        notes=f'Swap {swap_amount} OP via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=OP_ZEROX_ROUTER,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.OPTIMISM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=Asset('eip155:10/erc20:******************************************'),
        amount=FVal(received_amount),
        location_label=user_address,
        notes=f'Receive {received_amount} USDC.e as the result of a swap via the 0x protocol',
        counterparty=CPT_ZEROX,
        address=OP_ZEROX_ROUTER,
    )]


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_swap_anon_event(ethereum_inquirer: 'EthereumInquirer', ethereum_accounts):
    """zerox has a special contract for swaps that emits an anonymous event that causes the
    logic of some post decoding rules to fail. This is a regression test for this contract.
    """
    tx_hash = deserialize_evm_tx_hash('0x5871cd5d19d749135ac563eddb4cb04bd0d13f05414666a887b1628f5968b7dc')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    assert events == [EvmEvent(
        tx_hash=tx_hash,
        sequence_index=0,
        timestamp=(timestamp := TimestampMS(*************)),
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.SPEND,
        asset=Asset('eip155:1/erc20:******************************************'),
        amount=FVal(swap_amount := '7720.393471610974409838'),
        location_label=ethereum_accounts[0],
        notes=f'Swap {swap_amount} MORPHO in a cowswap twap order',
        counterparty=CPT_COWSWAP,
        address=(address := string_to_evm_address('******************************************')),
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=1,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.RECEIVE,
        asset=Asset('eip155:1/erc20:******************************************'),
        amount=FVal(received_amount := '17631.876781'),
        location_label=ethereum_accounts[0],
        notes=f'Receive {received_amount} USDC as the result of a cowswap twap order',
        counterparty=CPT_COWSWAP,
        address=address,
    ), EvmEvent(
        tx_hash=tx_hash,
        sequence_index=2,
        timestamp=timestamp,
        location=Location.ETHEREUM,
        event_type=HistoryEventType.TRADE,
        event_subtype=HistoryEventSubType.FEE,
        asset=Asset('eip155:1/erc20:******************************************'),
        amount=FVal(fee_amount := '14.050054845407416491'),
        location_label=ethereum_accounts[0],
        notes=f'Spend {fee_amount} MORPHO as a cowswap fee',
        counterparty=CPT_COWSWAP,
        address=address,
    )]
