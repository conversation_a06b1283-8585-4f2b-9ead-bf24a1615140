import pytest

from rotkehlchen.assets.asset import Asset, EvmToken
from rotkehlchen.chain.ethereum.modules.safe.constants import (
    CPT_SAFE,
    SAFE_LOCKING,
    SAFE_VESTING,
    SAFEPASS_AIRDROP,
)
from rotkehlchen.chain.evm.decoding.constants import CPT_GAS
from rotkehlchen.chain.evm.decoding.safe.constants import CPT_SAFE_MULTISIG
from rotkehlchen.chain.evm.types import string_to_evm_address
from rotkehlchen.constants.assets import A_ETH
from rotkehlchen.constants.misc import ZERO
from rotkehlchen.fval import FVal
from rotkehlchen.history.events.structures.evm_event import EvmEvent
from rotkehlchen.history.events.structures.types import HistoryEventSubType, HistoryEventType
from rotkehlchen.tests.utils.ethereum import get_decoded_events_of_transaction
from rotkehlchen.types import Location, TimestampMS, deserialize_evm_tx_hash


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [
    [
        '******************************************',
        '******************************************',
    ],
])
def test_added_owner(ethereum_inquirer, ethereum_accounts):
    tx_hex = deserialize_evm_tx_hash('0x2702bb7cf56d012b9bc85d66428a361d560172a5e519384e7c507db22d07090f')  # noqa: E501
    evmhash = deserialize_evm_tx_hash(tx_hex)
    user_address = ethereum_accounts[0]
    multisig_address = ethereum_accounts[1]
    new_owner = '******************************************'
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hex)
    timestamp = TimestampMS(*************)
    gas_amount_str = '0.*********'
    expected_events = [
        EvmEvent(
            tx_hash=evmhash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas_amount_str),
            location_label=user_address,
            notes=f'Burn {gas_amount_str} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=153,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.NONE,
            asset=A_ETH,
            amount=ZERO,
            location_label=user_address,
            notes=f'Add owner {new_owner} to multisig {multisig_address}',
            counterparty=CPT_SAFE_MULTISIG,
            address=multisig_address,
        ),
    ]
    assert events == expected_events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [
    [
        '******************************************',
        '******************************************',
    ],
])
def test_removed_owner(ethereum_inquirer, ethereum_accounts):
    tx_hex = deserialize_evm_tx_hash('0x868a3c64eb7e68a0c0fde4ec94f7825f1400ebba9aeefc284771b0136cbd72dd')  # noqa: E501
    evmhash = deserialize_evm_tx_hash(tx_hex)
    user_address = ethereum_accounts[0]
    multisig_address = ethereum_accounts[1]
    removed_owner = '******************************************'
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hex)
    timestamp = TimestampMS(*************)
    gas_amount_str = '0.********'
    expected_events = [
        EvmEvent(
            tx_hash=evmhash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas_amount_str),
            location_label=user_address,
            notes=f'Burn {gas_amount_str} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=101,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.NONE,
            asset=A_ETH,
            amount=ZERO,
            location_label=user_address,
            notes=f'Remove owner {removed_owner} from multisig {multisig_address}',
            counterparty=CPT_SAFE_MULTISIG,
            address=multisig_address,
        ),
    ]
    assert events == expected_events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [
    [
        '******************************************',
        '******************************************',
    ],
])
def test_changed_threshold(ethereum_inquirer, ethereum_accounts):
    tx_hex = deserialize_evm_tx_hash('0x8332c637f98362dea0885f744f121d09ac5c548603f833b9d0bd9513fa637c52')  # noqa: E501
    evmhash = deserialize_evm_tx_hash(tx_hex)
    user_address = ethereum_accounts[0]
    multisig_address = ethereum_accounts[1]
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hex)
    timestamp = TimestampMS(*************)
    gas_amount_str = '0.*********'
    expected_events = [
        EvmEvent(
            tx_hash=evmhash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas_amount_str),
            location_label=user_address,
            notes=f'Burn {gas_amount_str} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=59,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.NONE,
            asset=A_ETH,
            amount=ZERO,
            location_label=user_address,
            notes=f'Change signers threshold to 3 for multisig {multisig_address}',
            counterparty=CPT_SAFE_MULTISIG,
            address=multisig_address,
        ),
    ]
    assert events == expected_events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_execution_success(ethereum_inquirer, ethereum_accounts):
    """
    Test that a successful safe transaction execution shows something if the executor is tracked
    """
    tx_hex = deserialize_evm_tx_hash('0x7bfaa362453a9320243d7f604b7ffff10c31964a62e779a8cd280987b203875f')  # noqa: E501
    evmhash = deserialize_evm_tx_hash(tx_hex)
    user_address = ethereum_accounts[0]
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hex)
    multisig_address = '******************************************'
    timestamp = TimestampMS(*************)
    gas_amount_str = '0.006953999441541852'
    expected_events = [
        EvmEvent(
            tx_hash=evmhash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas_amount_str),
            location_label=user_address,
            notes=f'Burn {gas_amount_str} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=194,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.NONE,
            asset=A_ETH,
            amount=ZERO,
            location_label=user_address,
            notes=f'Successfully executed safe transaction 0xd273f3d34c8d075300dfe3aaa9d3dff76bff7ca4d874846c8bd41c2ab100118f for multisig {multisig_address}',  # noqa: E501
            counterparty=CPT_SAFE_MULTISIG,
            address=multisig_address,
        ),
    ]
    assert events == expected_events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_execution_failure(ethereum_inquirer, ethereum_accounts):
    """
    Test that a failed safe transaction execution shows something if the executor is tracked
    """
    tx_hex = deserialize_evm_tx_hash('0xf4b387bac0e6fa05b811098fb747297bdb9ce06152aa9e841750a85ed4d4bece')  # noqa: E501
    evmhash = deserialize_evm_tx_hash(tx_hex)
    user_address = ethereum_accounts[0]
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hex)
    multisig_address = '******************************************'
    timestamp = TimestampMS(*************)
    gas_amount_str = '0.*********'
    expected_events = [
        EvmEvent(
            tx_hash=evmhash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas_amount_str),
            location_label=user_address,
            notes=f'Burn {gas_amount_str} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=217,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.NONE,
            asset=A_ETH,
            amount=ZERO,
            location_label=user_address,
            notes=f'Failed to execute safe transaction 0x4663a55668527aaa4c7811e8277c7258b5e43af4269a651b1d1cf6ab24293b1e for multisig {multisig_address}',  # noqa: E501
            counterparty=CPT_SAFE_MULTISIG,
            address=multisig_address,
        ),
    ]
    assert events == expected_events


@pytest.mark.vcr
@pytest.mark.parametrize('ethereum_accounts', [['******************************************']])
def test_safe_creation(ethereum_inquirer, ethereum_accounts):
    """Test that creation of new safes is tracked"""
    tx_hex = deserialize_evm_tx_hash('0xa9e3c581f39403a0a2eb5a3e604be715c0a4ee8aa4bcc9bddece5c268b47e233')  # noqa: E501
    evmhash = deserialize_evm_tx_hash(tx_hex)
    user_address = ethereum_accounts[0]
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hex)
    timestamp = TimestampMS(*************)
    gas_amount_str = '0.004928138478008416'
    expected_events = [
        EvmEvent(
            tx_hash=evmhash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas_amount_str),
            location_label=user_address,
            notes=f'Burn {gas_amount_str} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=171,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.CREATE,
            asset=A_ETH,
            amount=ZERO,
            location_label=user_address,
            notes='Create a new safe with a threshold of 2 and owners ******************************************,******************************************',  # noqa: E501
            counterparty=CPT_SAFE_MULTISIG,
            address=string_to_evm_address('******************************************'),
        ),
    ]
    assert events == expected_events


@pytest.mark.vcr
@pytest.mark.parametrize('polygon_pos_accounts', [['******************************************']])  # yabir.eth  # noqa: E501
def test_safe_spam(polygon_pos_inquirer, polygon_pos_accounts):
    """Test that a safe transaction if from an unrelated account, does not appear in events"""
    tx_hex = deserialize_evm_tx_hash('0xefb07f4d166d6887eada96e61fd6821bfdf889d5435d75ab44d4ca0fa7627396')  # noqa: E501
    evmhash = deserialize_evm_tx_hash(tx_hex)
    user_address = polygon_pos_accounts[0]
    events, _ = get_decoded_events_of_transaction(
        evm_inquirer=polygon_pos_inquirer,
        tx_hash=tx_hex,
    )
    timestamp = TimestampMS(*************)
    amount_str = '0.*****************'
    spam_contract = '******************************************'
    expected_events = [
        EvmEvent(
            tx_hash=evmhash,
            sequence_index=431,
            timestamp=timestamp,
            location=Location.POLYGON_POS,
            event_type=HistoryEventType.RECEIVE,
            event_subtype=HistoryEventSubType.NONE,
            asset=Asset('eip155:137/erc20:******************************************'),
            amount=FVal(amount_str),
            location_label=user_address,
            notes=f'Receive {amount_str} QI from {spam_contract} to {user_address}',
            address=spam_contract,
        ),
    ]
    assert events == expected_events


@pytest.mark.vcr(filter_query_parameters=['apikey'])
@pytest.mark.parametrize('ethereum_accounts', [[
    '******************************************',
    '******************************************',
]])
def test_safe_vesting_claim(ethereum_inquirer, ethereum_accounts):
    tx_hex = deserialize_evm_tx_hash('0xc831d94b43be533e83562da9bc10b38b4bab6ce6046c3a9baf76c5359634625a')  # noqa: E501
    evmhash = deserialize_evm_tx_hash(tx_hex)
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hex)
    user_address, multisig_address, timestamp, gas, amount = ethereum_accounts[0], ethereum_accounts[1], TimestampMS(*************), '0.*****************', '20549.221611721611721612'  # noqa: E501
    expected_events = [
        EvmEvent(
            tx_hash=evmhash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas),
            location_label=user_address,
            notes=f'Burn {gas} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=270,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.RECEIVE,
            event_subtype=HistoryEventSubType.NONE,
            asset=EvmToken('eip155:1/erc20:******************************************'),
            amount=FVal(amount),
            location_label=multisig_address,
            notes=f'Claim {amount} SAFE from vesting',
            counterparty=CPT_SAFE,
            address=SAFE_VESTING,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=271,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.NONE,
            asset=A_ETH,
            amount=ZERO,
            location_label=user_address,
            notes=f'Successfully executed safe transaction 0x4abbfbca46ad4a4099ae7896cfd9e4a4c3ef604236d55efd58a0314e1bfcf0eb for multisig {multisig_address}',  # noqa: E501
            counterparty=CPT_SAFE_MULTISIG,
            address=multisig_address,
        ),
    ]
    assert events == expected_events


@pytest.mark.vcr(filter_query_parameters=['apikey'])
@pytest.mark.parametrize('ethereum_accounts', [[
    '******************************************',
    '******************************************',
]])
def test_safe_lock(ethereum_inquirer, ethereum_accounts):
    tx_hex = deserialize_evm_tx_hash('0xad3d976ae02cf82f109cc2d2f3e8f2f10df6a00a4825e3f04cf0e1b7e68a06b8')  # noqa: E501
    evmhash = deserialize_evm_tx_hash(tx_hex)
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hex)
    user_address, multisig_address, timestamp, gas, amount = ethereum_accounts[0], ethereum_accounts[1], TimestampMS(*************), '0.*****************', '5115.763372'  # noqa: E501
    expected_events = [
        EvmEvent(
            tx_hash=evmhash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas),
            location_label=user_address,
            notes=f'Burn {gas} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=157,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.APPROVE,
            asset=EvmToken('eip155:1/erc20:******************************************'),
            amount=FVal(amount),
            location_label=multisig_address,
            notes=f'Set SAFE spending approval of {multisig_address} by {SAFE_LOCKING} to {amount}',  # noqa: E501
            address=SAFE_LOCKING,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=158,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.APPROVE,
            asset=EvmToken('eip155:1/erc20:******************************************'),
            amount=ZERO,
            location_label=multisig_address,
            notes=f'Revoke SAFE spending approval of {multisig_address} by {SAFE_LOCKING}',
            address=SAFE_LOCKING,
        ), EvmEvent(

            tx_hash=evmhash,
            sequence_index=159,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.DEPOSIT,
            event_subtype=HistoryEventSubType.DEPOSIT_ASSET,
            asset=EvmToken('eip155:1/erc20:******************************************'),
            amount=FVal(amount),
            location_label=multisig_address,
            notes=f'Lock {amount} SAFE for Safe{{Pass}}',
            counterparty=CPT_SAFE,
            address=SAFE_LOCKING,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=161,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.NONE,
            asset=A_ETH,
            amount=ZERO,
            location_label=user_address,
            notes=f'Successfully executed safe transaction 0x4aed5d3d1e4a41a5dec570127ff5bba82d40214ce4fc6f65767ee3c2ac17aaca for multisig {multisig_address}',  # noqa: E501
            counterparty=CPT_SAFE_MULTISIG,
            address=multisig_address,
        ),
    ]
    assert events == expected_events


@pytest.mark.vcr(filter_query_parameters=['apikey'])
@pytest.mark.parametrize('ethereum_accounts', [[
    '******************************************',
    '******************************************',
]])
def test_safe_unlock(ethereum_inquirer, ethereum_accounts):
    tx_hex = deserialize_evm_tx_hash('0x51d4c06ff00be729fe5bc79215253e45e65ce4c8531cd249633c6e76754c89d0')  # noqa: E501
    evmhash = deserialize_evm_tx_hash(tx_hex)
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hex)
    user_address, multisig_address, timestamp, gas, amount = ethereum_accounts[0], ethereum_accounts[1], TimestampMS(*************), '0.0003433', '1026.126150242296748346'  # noqa: E501
    expected_events = [
        EvmEvent(
            tx_hash=evmhash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas),
            location_label=user_address,
            notes=f'Burn {gas} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=560,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.NONE,
            asset=EvmToken('eip155:1/erc20:******************************************'),
            amount=FVal(amount),
            location_label=multisig_address,
            notes=f'Start unlock of {amount} SAFE from Safe{{Pass}}',
            counterparty=CPT_SAFE,
            address=SAFE_LOCKING,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=561,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.NONE,
            asset=A_ETH,
            amount=ZERO,
            location_label=user_address,
            notes=f'Successfully executed safe transaction 0x3b6ade06b3f4dd85a4056813f6c418a6780fa675a69c728fad28e88a7726c9dd for multisig {multisig_address}',  # noqa: E501
            counterparty=CPT_SAFE_MULTISIG,
            address=multisig_address,
        ),
    ]
    assert events == expected_events


@pytest.mark.vcr(filter_query_parameters=['apikey'])
@pytest.mark.parametrize('ethereum_accounts', [[
    '******************************************',
    '******************************************',
]])
def test_safe_withdraw_unlocked(ethereum_inquirer, ethereum_accounts):
    tx_hex = deserialize_evm_tx_hash('0x9520c7e117225afc930d1092bf35c17e6726c6564ed4e757eeb6a3c29d10304b')  # noqa: E501
    evmhash = deserialize_evm_tx_hash(tx_hex)
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hex)
    user_address, multisig_address, timestamp, gas, amount = ethereum_accounts[0], ethereum_accounts[1], TimestampMS(*************), '0.*****************', '2404.451820314008697626'  # noqa: E501
    expected_events = [
        EvmEvent(
            tx_hash=evmhash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas),
            location_label=user_address,
            notes=f'Burn {gas} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=146,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.WITHDRAWAL,
            event_subtype=HistoryEventSubType.REMOVE_ASSET,
            asset=EvmToken('eip155:1/erc20:******************************************'),
            amount=FVal(amount),
            location_label=multisig_address,
            notes=f'Withdraw {amount} SAFE from Safe{{Pass}} locking',
            counterparty=CPT_SAFE,
            address=SAFE_LOCKING,
        ), EvmEvent(
            tx_hash=evmhash,
            sequence_index=147,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.NONE,
            asset=A_ETH,
            amount=ZERO,
            location_label=user_address,
            notes=f'Successfully executed safe transaction 0x1f5ea0fb049fabccf49f2a9e8ccd8ae95db0d32727d5c62ea329b0381a51a698 for multisig {multisig_address}',  # noqa: E501
            counterparty=CPT_SAFE_MULTISIG,
            address=multisig_address,
        ),
    ]
    assert events == expected_events


@pytest.mark.vcr(filter_query_parameters=['apikey'])
@pytest.mark.parametrize('ethereum_accounts', [[
    '******************************************',
    '******************************************',
]])
def test_safepass_start_vesting_claim(ethereum_inquirer, ethereum_accounts):
    tx_hash = deserialize_evm_tx_hash('0xfd07173651763370557d8300a8f5891d26ec7055238d6daf4f53c3f060d0f42d')  # noqa: E501
    events, _ = get_decoded_events_of_transaction(evm_inquirer=ethereum_inquirer, tx_hash=tx_hash)
    user_address, multisig_address, timestamp, gas = ethereum_accounts[0], ethereum_accounts[1], TimestampMS(*************), '0.*****************'  # noqa: E501
    expected_events = [
        EvmEvent(
            tx_hash=tx_hash,
            sequence_index=0,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.SPEND,
            event_subtype=HistoryEventSubType.FEE,
            asset=A_ETH,
            amount=FVal(gas),
            location_label=user_address,
            notes=f'Burn {gas} ETH for gas',
            counterparty=CPT_GAS,
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=118,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.NONE,
            asset=EvmToken('eip155:1/erc20:******************************************'),
            amount=ZERO,
            location_label=multisig_address,
            notes='Claim and start vesting of SAFE tokens from Safe{Pass}',
            counterparty=CPT_SAFE,
            address=SAFEPASS_AIRDROP,
        ), EvmEvent(
            tx_hash=tx_hash,
            sequence_index=119,
            timestamp=timestamp,
            location=Location.ETHEREUM,
            event_type=HistoryEventType.INFORMATIONAL,
            event_subtype=HistoryEventSubType.NONE,
            asset=A_ETH,
            amount=ZERO,
            location_label=user_address,
            notes=f'Successfully executed safe transaction 0x8e01ca76365b063a7628f0072527f51579660103276990ab4f2b97e2de26e04b for multisig {multisig_address}',  # noqa: E501
            counterparty=CPT_SAFE_MULTISIG,
            address=multisig_address,
        ),
    ]
    assert events == expected_events
