from typing import Final

from rotkehlchen.chain.evm.types import string_to_evm_address

AAVE_V3_DATA_PROVIDER_OLD: Final = string_to_evm_address('******************************************')  # noqa: E501
# Those can be found in https://search.onaave.com/?q=protocol%20data%20provider
AAVE_V3_DATA_PROVIDER: Final = string_to_evm_address('******************************************')
LIDO_AAVE_V3_DATA_PROVIDER: Final = string_to_evm_address('******************************************')  # noqa: E501
ETHERFI_AAVE_V3_DATA_PROVIDER: Final = string_to_evm_address('******************************************')  # noqa: E501
