from typing import Final

from rotkehlchen.assets.asset import Asset
from rotkehlchen.chain.evm.types import string_to_evm_address
from rotkehlchen.constants.assets import (
    A_BSC_BNB,
    A_DAI,
    A_ETH,
    A_ETH_EURE,
    A_EUR,
    A_OP,
    A_POLYGON_POS_MATIC,
    A_USD,
    A_USDC,
    A_USDT,
    A_WBNB,
    A_WETH,
    A_WETH_ARB,
    A_WETH_BASE,
    A_WETH_OPT,
    A_WETH_POLYGON,
    A_WMATIC,
)
from rotkehlchen.types import ChainID

SINGLE_SIDE_USD_POOL_LIMIT: Final = 5000
UNISWAP_ASSET_TO_EVM_ASSET: Final = {
    A_USD: A_USDC,
    A_EUR: A_ETH_EURE,
    A_ETH: A_WETH,
    A_BSC_BNB: A_WBNB,
    A_POLYGON_POS_MATIC: A_WMATIC,
}

A_POLYGON_USDC: Final = Asset('eip155:137/erc20:******************************************')
A_POLYGON_POS_USDT: Final = Asset('eip155:137/erc20:******************************************')
A_ARBITRUM_USDC: Final = Asset('eip155:42161/erc20:******************************************')
A_ARBITRUM_USDT: Final = Asset('eip155:42161/erc20:******************************************')
A_OPTIMISM_USDC: Final = Asset('eip155:10/erc20:******************************************')
A_OPTIMISM_USDT: Final = Asset('eip155:10/erc20:******************************************')
A_BASE_USDC: Final = Asset('eip155:8453/erc20:******************************************')
A_BSC_USDC: Final = Asset('eip155:56/erc20:******************************************')
A_BSC_USDT: Final = Asset('eip155:56/erc20:******************************************')
A_BSC_ETH: Final = Asset('eip155:56/erc20:******************************************')

UNISWAP_ETH_ASSETS: Final = {
    ChainID.ETHEREUM: A_WETH,
    ChainID.POLYGON_POS: A_WETH_POLYGON,
    ChainID.ARBITRUM_ONE: A_WETH_ARB,
    ChainID.OPTIMISM: A_WETH_OPT,
    ChainID.BASE: A_WETH_BASE,
    ChainID.BINANCE_SC: A_BSC_ETH,
}
UNISWAP_ROUTING_ASSETS: Final = {
    ChainID.ETHEREUM: [A_WETH, A_DAI, A_USDT],
    ChainID.POLYGON_POS: [A_POLYGON_POS_MATIC, A_WETH_POLYGON, A_POLYGON_USDC, A_POLYGON_POS_USDT],
    ChainID.ARBITRUM_ONE: [A_WETH_ARB, A_ARBITRUM_USDC, A_ARBITRUM_USDT],
    ChainID.OPTIMISM: [A_OP, A_WETH_OPT, A_OPTIMISM_USDC, A_OPTIMISM_USDT],
    ChainID.BASE: [A_WETH_BASE, A_BASE_USDC],
    ChainID.BINANCE_SC: [A_WBNB, A_BSC_USDC, A_BSC_USDT],
}
UNISWAP_FACTORY_ADDRESSES: Final = {
    2: {  # V2 factories
        ChainID.ETHEREUM: string_to_evm_address('******************************************'),
        ChainID.POLYGON_POS: string_to_evm_address('******************************************'),
        ChainID.ARBITRUM_ONE: string_to_evm_address('******************************************'),
        ChainID.OPTIMISM: string_to_evm_address('******************************************'),
        ChainID.BASE: string_to_evm_address('******************************************'),
        ChainID.BINANCE_SC: string_to_evm_address('******************************************'),
    },
    3: {  # V3 factories
        ChainID.ETHEREUM: string_to_evm_address('******************************************'),
        ChainID.POLYGON_POS: string_to_evm_address('******************************************'),
        ChainID.ARBITRUM_ONE: string_to_evm_address('******************************************'),
        ChainID.OPTIMISM: string_to_evm_address('******************************************'),
        ChainID.BASE: string_to_evm_address('******************************************'),
        ChainID.BINANCE_SC: string_to_evm_address('******************************************'),
    },
}
UNISWAP_SUPPORTED_CHAINS: Final = {
    ChainID.ETHEREUM,
    ChainID.POLYGON_POS,
    ChainID.ARBITRUM_ONE,
    ChainID.OPTIMISM,
    ChainID.BASE,
    ChainID.BINANCE_SC,
}
