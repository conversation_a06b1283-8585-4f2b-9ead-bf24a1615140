from typing import Final

from rotke<PERSON>chen.chain.evm.decoding.types import CounterpartyDetails

CPT_HOP: Final = 'hop'
HOP_CPT_DETAILS: Final = CounterpartyDetails(
    identifier=CPT_HOP,
    label='Hop Protocol',
    image='hop_protocol.png',
)

TRANSFER_FROM_L1_COMPLETED: Final = b'2\tX\x17i0\x80N\xb6l#C\xc74?\xc06}\xc1bIY\x0c\x0f\x19W\x83\xbe\xe1\x99\xd0\x94'  # noqa: E501
WITHDRAWAL_BONDED: Final = b'\x0c=%\x0cx1\x05\x1ex\xaajVg\x9eY\x03t\xc7\xc4$A_\xfeJ\xa4tI\x1d\xef/\xe7\x05'  # noqa: E501
TRANSFER_SENT: Final = b'\xe3]\xdd\xd4\xeau\xd7\xe9\xb3\xfe\x93\xafON@\xe7x\xc3\xda@t\xc9\xd9>|e6\xf1\xe8\x03\xc1\xeb'  # noqa: E501
WITHDREW: Final = b'\x94u\xcd\xbd\xe5\xfcq\xfe,\xcdA<\x82\x87\x8e\xe5M\x06\x1b\x9ft\xf9\xe2\xe1\xa0?\xf1\x17\x88!P,'  # noqa: E501
TOKEN_SWAP: Final = b'\xc6\xc1\xe0c\r\xbe\x910\xcc\x06\x80(Hl\r\x11\x8d\xdc\xea4\x85P\x81\x9d\xef\xd5\xcb\x8c%\x7f\x8a8'  # noqa: E501
ADD_LIQUIDITY: Final = b'\x18\x9cb;fk\x1bE\xb8=qx\xf3\x9b\x8c\x08|\xb0\x97t1|\xa2\xf5<-<7&\xf2"\xa2'  # noqa: E501
REMOVE_LIQUIDITY_ONE: Final = b'C\xfb\x02\x99\x8fN\x03\xda.\x0eo\xffS\xfd\xbf\x0c@\xa9\xf4_\x14]\xc3w\xfc0a]}z\x8ad'  # noqa: E501
REMOVE_LIQUIDITY: Final = b'\x88\xd3\x8e\xd5\x98\xfd\xd8\t\xc2\xbf\x01\xeeI\xcd$\xb7\xfd\xab\xf3y\xa8=)VyR\xb6\x03$\xd5\x8c\xef'  # noqa: E501
REWARDS_PAID: Final = b'\xe2@6@\xbah\xfe\xd3\xa2\xf8\x8buWU\x1d\x19\x93\xf8K\x99\xbb\x10\xff\x83?\x0c\xf8\xdb\x0c^\x04\x86'  # noqa: E501
