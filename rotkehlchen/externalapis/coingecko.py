import json
import logging
from http import HTTPStatus
from typing import TYPE_CHECKING, Any, Literal, NamedTuple, overload

import requests

from rotkehlchen.assets.asset import Asset, AssetWithOracles
from rotkehlchen.constants.prices import ZERO_PRICE
from rotkehlchen.constants.resolver import evm_address_to_identifier, strethaddress_to_identifier
from rotkehlchen.constants.timing import DAY_IN_SECONDS, YEAR_IN_SECONDS
from rotkehlchen.db.settings import CachedSettings
from rotkehlchen.errors.asset import UnknownAsset, UnsupportedAsset
from rotkehlchen.errors.misc import RemoteError
from rotkehlchen.errors.price import NoPriceForGivenTimestamp, PriceQueryUnsupportedAsset
from rotkehlchen.externalapis.interface import ExternalServiceWithApiKeyOptionalDB
from rotkehlchen.fval import FVal
from rotkehlchen.interfaces import HistoricalPriceOracleWithCoinListInterface
from rotkehlchen.logging import Rot<PERSON><PERSON>chenLogsAdapter
from rotkehlchen.types import ChainID, EvmT<PERSON>Kind, ExternalService, Price, Timestamp
from rotkehlchen.utils.misc import set_user_agent, timestamp_to_date, ts_now
from rotkehlchen.utils.mixins.penalizable_oracle import PenalizablePriceOracleMixin
from rotkehlchen.utils.network import create_session

if TYPE_CHECKING:
    from rotkehlchen.db.dbhandler import DBHandler

logger = logging.getLogger(__name__)
log = RotkehlchenLogsAdapter(logger)


class CoingeckoAssetData(NamedTuple):
    identifier: str
    symbol: str
    name: str
    image_url: str


DELISTED_ASSETS = {
    strethaddress_to_identifier('******************************************'),
    'FLUZ',
    'EBCH',
    'GOLOS',
    'NPER',
    strethaddress_to_identifier('******************************************'),
    'ADN',
    'PIX',
    strethaddress_to_identifier('******************************************'),
    'LKY',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    'BITCAR',
    strethaddress_to_identifier('******************************************'),
    'OLE',
    'ROC',
    'VIN',
    'FIH',
    strethaddress_to_identifier('******************************************'),
    'ADH',
    'AUR',
    strethaddress_to_identifier('******************************************'),
    'BYC',
    'DGS',
    strethaddress_to_identifier('******************************************'),
    'HST',
    'INS',
    'IPSX',
    'SHP',
    'WDC',
    'BOST',
    'FND',
    'LDC',
    'ORI',
    'RIPT',
    'SGR',
    'LOCUS',
    'REDC',
    'SGN',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    'AC',
    strethaddress_to_identifier('******************************************'),
    'BITPARK',
    strethaddress_to_identifier('******************************************'),
    'DAN',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    'DROP',
    'ERD',
    'ETBS',
    strethaddress_to_identifier('******************************************'),
    'STP',
    'SYNC',
    'TBT',
    'TNT',
    'WIC',
    'XCN',
    strethaddress_to_identifier('******************************************'),
    'FREC',
    'PTC',
    strethaddress_to_identifier('******************************************'),
    'J8T',
    'MRK',
    'TTV',
    'ALX',
    'EBC',
    'RCN-2',
    'SKYM',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    'aLEND',
    'aREP',
    'CRBT',
    'EXC-2',
    strethaddress_to_identifier('******************************************'),
    'CZR',
    'ROCK2',
    'ATMI',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    'CREDO',
    'ETK',
    'FNKOS',
    strethaddress_to_identifier('******************************************'),
    'GIM',
    strethaddress_to_identifier('******************************************'),
    'KORE',
    'NBAI',
    strethaddress_to_identifier('******************************************'),
    'XEL',
    'ATS',
    'BCY',
    'yDAI+yUSDC+yUSDT+yBUSD',
    'yyDAI+yUSDC+yUSDT+yBUSD',
    'ypaxCrv',
    'crvRenWBTC',
    'crvRenWSBTC',
    'ycrvRenWSBTC',
    'SPRK',
    'VV',
    'DRP',
    'HBZ',
    'TAAS',
    'TRUMPLOSE',
    'TRUMPWIN',
    'ATX-2',
    'SONIQ',
    'TRUST',
    'CDX',
    'CRB',
    'CTX',
    'EGC',
    'ICOS',
    'MTRC',
    'NOX',
    'PHI',
    'RLT',
    'SPIN',
    'VIEW',
    'VRM',
    strethaddress_to_identifier('******************************************'),
    'SPF',
    'NOBS',
    'DADI',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    'FLO',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    'FCN',
    'BITB',
    'SMC',
    'POP',
    'DRM',
    'CRYPT',
    'CPC-2',
    'BSD',
    'BITS',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    'BTCS',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    'ARB-2',
    'BAY',
    'UNITY',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    'DCT',
    'NOTE',
    'SLR',
    'SXC',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    'XVC',
    'MCN',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    'FAIR',
    'BUX',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    'BBR',
    'CNL',
    'DOPE',
    'ECC',
    'SDT',
    'TEDDY',
    'ICA',
    'BAG',
    'MCT',
    'LOL',
    strethaddress_to_identifier('******************************************'),
    'eip155:56/erc20:******************************************',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    'TOR',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.BINANCE_SC, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.BINANCE_SC, token_type=EvmTokenKind.ERC20),  # noqa: E501
    'BBK-2',
    'IFC',
    'MEC',
    'PAI',
    'PPC',
    'HOLY',
    'COIN',
    'WNDR',
    'DESO',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.BINANCE_SC, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.BINANCE_SC, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.BINANCE_SC, token_type=EvmTokenKind.ERC20),  # noqa: E501
    strethaddress_to_identifier('******************************************'),
    'SILK',
    'CRT',
    'EAC',
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('0x47b28F365Bf4CB38DB4B6356864BDE7bc4B35129'),
    strethaddress_to_identifier('0x687174f8C49ceb7729D925C3A961507ea4Ac7b28'),
    strethaddress_to_identifier('0x13119E34E140097a507B07a5564bDe1bC375D9e6'),
    strethaddress_to_identifier('0x123aB195DD38B1b40510d467a6a359b201af056f'),
    strethaddress_to_identifier('0x6BEB418Fc6E1958204aC8baddCf109B8E9694966'),
    strethaddress_to_identifier('0xb0dFd28d3CF7A5897C694904Ace292539242f858'),
    strethaddress_to_identifier('0x5dbe296F97B23C4A6AA6183D73e574D02bA5c719'),
    strethaddress_to_identifier('0x01F2AcF2914860331C1Cb1a9AcecDa7475e06Af8'),
    strethaddress_to_identifier('0x5D4d57cd06Fa7fe99e26fdc481b468f77f05073C'),
    strethaddress_to_identifier('0x8064d9Ae6cDf087b1bcd5BDf3531bD5d8C537a68'),
    strethaddress_to_identifier('0x832904863978b94802123106e6eB491BDF0Df928'),
    strethaddress_to_identifier('0xBb1f24C0c1554b9990222f036b0AaD6Ee4CAec29'),
    strethaddress_to_identifier('0x20F7A3DdF244dc9299975b4Da1C39F8D5D75f05A'),
    strethaddress_to_identifier('0xF70a642bD387F94380fFb90451C2c81d4Eb82CBc'),
    strethaddress_to_identifier('0xBAE235823D7255D9D48635cEd4735227244Cd583'),
    strethaddress_to_identifier('0xE4E822C0d5b329E8BB637972467d2E313824eFA0'),
    strethaddress_to_identifier('0x6bC1F3A1ae56231DbB64d3E82E070857EAe86045'),
    strethaddress_to_identifier('0x7025baB2EC90410de37F488d1298204cd4D6b29d'),
    strethaddress_to_identifier('******************************************'),
    strethaddress_to_identifier('0xEd025A9Fe4b30bcd68460BCA42583090c2266468'),
    strethaddress_to_identifier('0xeEd4d7316a04ee59de3d301A384262FFbDbd589a'),
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.BINANCE_SC, token_type=EvmTokenKind.ERC20),  # noqa: E501
    strethaddress_to_identifier('******************************************'),
    'FB',
    'ROAD',
    'SPD-2',
    'PLA',
    'MER',
    'DYN',
    'CMT',
    'BLU',
    'ARC',
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.BINANCE_SC, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.BINANCE_SC, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ARBITRUM_ONE, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.BINANCE_SC, token_type=EvmTokenKind.ERC20),  # noqa: E501
    'TFC',
    'DON',
    'NUT',
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ARBITRUM_ONE, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.BINANCE_SC, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.BINANCE_SC, token_type=EvmTokenKind.ERC20),  # noqa: E501
    evm_address_to_identifier(address='******************************************', chain_id=ChainID.ETHEREUM, token_type=EvmTokenKind.ERC20),  # noqa: E501
}

COINGECKO_SIMPLE_VS_CURRENCIES = {
    'btc',
    'eth',
    'ltc',
    'bch',
    'bnb',
    'eos',
    'xrp',
    'xlm',
    'link',
    'dot',
    'yfi',
    'usd',
    'aed',
    'ars',
    'aud',
    'bdt',
    'bhd',
    'bmd',
    'brl',
    'cad',
    'chf',
    'clp',
    'cny',
    'czk',
    'dkk',
    'eur',
    'gbp',
    'hkd',
    'huf',
    'idr',
    'ils',
    'inr',
    'jpy',
    'krw',
    'kwd',
    'lkr',
    'mmk',
    'mxn',
    'myr',
    'nok',
    'nzd',
    'php',
    'pkr',
    'pln',
    'rub',
    'sar',
    'sek',
    'sgd',
    'thb',
    'try',
    'twd',
    'uah',
    'vef',
    'vnd',
    'zar',
    'xdr',
    'xag',
    'xau',
}


class Coingecko(
        ExternalServiceWithApiKeyOptionalDB,
        HistoricalPriceOracleWithCoinListInterface,
        PenalizablePriceOracleMixin,
):

    def __init__(self, database: 'DBHandler | None') -> None:
        ExternalServiceWithApiKeyOptionalDB.__init__(self, database=database, service_name=ExternalService.COINGECKO)  # noqa: E501
        HistoricalPriceOracleWithCoinListInterface.__init__(self, oracle_name='coingecko')
        PenalizablePriceOracleMixin.__init__(self)
        self.session = create_session()
        set_user_agent(self.session)
        self.db: DBHandler | None  # type: ignore  # "solve" the self.db discrepancy

    @overload
    def _query(
            self,
            module: Literal['coins/list'],
            subpath: str | None = None,
            options: dict[str, str] | None = None,
    ) -> list[dict[str, Any]]:
        ...

    @overload
    def _query(
            self,
            module: Literal['coins', 'simple/price'],
            subpath: str | None = None,
            options: dict[str, str] | None = None,
    ) -> dict[str, Any]:
        ...

    def _query(
            self,
            module: str,
            subpath: str | None = None,
            options: dict[str, str] | None = None,
    ) -> dict[str, Any] | list[dict[str, Any]]:
        """Performs a coingecko query

        May raise:
        - RemoteError if there is a problem querying coingecko
        """
        if (api_key := self._get_api_key()) is not None:
            base_url = 'https://pro-api.coingecko.com/api/v3'
        else:
            base_url = 'https://api.coingecko.com/api/v3'

        if options is None:
            options = {}
        url = f'{base_url}/{module}/{subpath or ""}'
        if api_key:
            self.session.headers.update({'x-cg-pro-api-key': api_key})
        else:
            self.session.headers.pop('x-cg-pro-api-key', None)

        log.debug(f'Querying coingecko: {url=} with {options=}')
        try:
            response = self.session.get(
                url=url,
                params=options,
                timeout=CachedSettings().get_timeout_tuple(),
            )
        except requests.exceptions.RequestException as e:
            self.penalty_info.note_failure_or_penalize()
            raise RemoteError(f'Coingecko API request failed due to {e!s}') from e

        if response.status_code == HTTPStatus.TOO_MANY_REQUESTS:
            self.last_rate_limit = ts_now()
            msg = f'Got rate limited by coingecko querying {url}'
            log.warning(msg)
            raise RemoteError(message=msg, error_code=HTTPStatus.TOO_MANY_REQUESTS)

        if response.status_code != 200:
            msg = (
                f'Coingecko API request {response.url} failed with HTTP status '
                f'code: {response.status_code}'
            )
            raise RemoteError(msg)

        try:
            decoded_json = json.loads(response.text)
        except json.decoder.JSONDecodeError as e:
            msg = f'Invalid JSON in Coingecko response. {e}'
            raise RemoteError(msg) from e

        return decoded_json

    def asset_data(self, asset_coingecko_id: str) -> CoingeckoAssetData:
        """
        Query coingecko to retrieve the asset information related to the provided coingecko id
        May raise:
        - UnsupportedAsset() if the asset is not supported by coingecko
        - RemoteError if there is a problem querying coingecko
        """
        options = {
            # Include all localized languages in response (true/false) [default: true]
            'localization': 'false',
            # Include tickers data (true/false) [default: true]
            'tickers': 'false',
            # Include market_data (true/false) [default: true]
            'market_data': 'false',
            # Include communitydata (true/false) [default: true]
            'community_data': 'false',
            # Include developer data (true/false) [default: true]
            'developer_data': 'false',
            # Include sparkline 7 days data (eg. true, false) [default: false]
            'sparkline': 'false',
        }
        data = self._query(
            module='coins',
            subpath=asset_coingecko_id,
            options=options,
        )

        # https://github.com/PyCQA/pylint/issues/4739
        try:
            parsed_data = CoingeckoAssetData(
                identifier=asset_coingecko_id,
                symbol=data['symbol'],
                name=data['name'],
                image_url=data['image']['small'],
            )
        except KeyError as e:
            raise RemoteError(
                f'Missing expected key entry {e} in coingecko coin data response',
            ) from e

        return parsed_data

    def all_coins(self) -> dict[str, dict[str, Any]]:
        """Returns all coingecko assets

        May raise:
        - RemoteError if there is an error with reaching coingecko
        """
        if (data := self.maybe_get_cached_coinlist(considered_recent_secs=DAY_IN_SECONDS)) is None:
            data = {}
            response = self._query(module='coins/list')
            for entry in response:
                if entry['id'] in data:
                    log.warning(
                        f'Found duplicate coingecko identifier {entry["id"]} when querying '
                        f'the list of coingecko assets. Ignoring...',
                    )
                    continue

                identifier = entry.pop('id')
                data[identifier] = entry

            self.cache_coinlist(data)

        return data

    @staticmethod
    def check_vs_currencies(
            from_asset: AssetWithOracles,
            to_asset: AssetWithOracles,
            location: str,
    ) -> str | None:
        vs_currency = to_asset.identifier.lower()
        if vs_currency not in COINGECKO_SIMPLE_VS_CURRENCIES:
            log.warning(
                f'Tried to query coingecko {location} from {from_asset.identifier} '
                f'to {to_asset.identifier}. But to_asset is not supported',
            )
            return None

        return vs_currency

    def query_current_price(
            self,
            from_asset: AssetWithOracles,
            to_asset: AssetWithOracles,
    ) -> Price:
        """Returns a simple price for from_asset to to_asset in coingecko.

        Uses the simple/price endpoint of coingecko. If to_asset is not part of the
        coingecko simple vs currencies or if from_asset is not supported in coingecko
        price zero is returned.

        May raise:
        - RemoteError if there is a problem querying coingecko
        """
        vs_currency = Coingecko.check_vs_currencies(
            from_asset=from_asset,
            to_asset=to_asset,
            location='simple price',
        )
        if not vs_currency:
            return ZERO_PRICE

        try:
            from_coingecko_id = from_asset.to_coingecko()
        except UnsupportedAsset:
            log.warning(
                f'Tried to query coingecko simple price from {from_asset.identifier} '
                f'to {to_asset.identifier}. But from_asset is not supported in coingecko',
            )
            return ZERO_PRICE

        result = self._query(
            module='simple/price',
            options={
                'ids': from_coingecko_id,
                'vs_currencies': vs_currency,
            })

        # https://github.com/PyCQA/pylint/issues/4739
        try:
            return Price(FVal(result[from_coingecko_id][vs_currency]))
        except KeyError as e:
            log.warning(
                f'Queried coingecko simple price from {from_asset.identifier} '
                f'to {to_asset.identifier}. But got key error for {e!s} when '
                f'processing the result.',
            )
            return ZERO_PRICE

    def can_query_history(
            self,
            from_asset: Asset,  # pylint: disable=unused-argument
            to_asset: Asset,
            timestamp: Timestamp,
            seconds: int | None = None,
    ) -> bool:
        """Apart from penalization, for coingecko if there is no paid API key then it won't
        allow you to query further than a year in history. So let's save ourselves network calls"""
        if self.api_key is None and ts_now() - timestamp > YEAR_IN_SECONDS:
            return False

        return not self.is_penalized()

    def query_historical_price(
            self,
            from_asset: Asset,
            to_asset: Asset,
            timestamp: Timestamp,
    ) -> Price:
        """
        May raise:
        - PriceQueryUnsupportedAsset if either from_asset or to_asset are not supported
        """
        try:
            from_asset = from_asset.resolve_to_asset_with_oracles()
            to_asset = to_asset.resolve_to_asset_with_oracles()
        except UnknownAsset as e:
            raise PriceQueryUnsupportedAsset(e.identifier) from e
        vs_currency = Coingecko.check_vs_currencies(
            from_asset=from_asset,
            to_asset=to_asset,
            location='historical price',
        )
        if not vs_currency:
            raise NoPriceForGivenTimestamp(
                from_asset=from_asset,
                to_asset=to_asset,
                time=timestamp,
            )

        try:
            from_coingecko_id = from_asset.to_coingecko()
        except UnsupportedAsset as e:
            log.warning(
                f'Tried to query coingecko historical price from {from_asset.identifier} '
                f'to {to_asset.identifier}. But from_asset is not supported in coingecko',
            )
            raise NoPriceForGivenTimestamp(
                from_asset=from_asset,
                to_asset=to_asset,
                time=timestamp,
            ) from e

        date = timestamp_to_date(timestamp, formatstr='%d-%m-%Y')
        result = self._query(
            module='coins',
            subpath=f'{from_coingecko_id}/history',
            options={
                'date': date,
                'localization': 'false',
            },
        )
        # https://github.com/PyCQA/pylint/issues/4739
        try:
            price = Price(FVal(result['market_data']['current_price'][vs_currency]))
        except KeyError as e:
            log.warning(
                f'Queried coingecko historical price from {from_asset.identifier} '
                f'to {to_asset.identifier}. But got key error for {e!s} when '
                f'processing the result.',
            )
            raise NoPriceForGivenTimestamp(
                from_asset=from_asset,
                to_asset=to_asset,
                time=timestamp,
                rate_limited=False,
            ) from e

        return price
