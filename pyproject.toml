[build-system]
requires = ["setuptools==67.3.2", "setuptools-scm==7.1.0", "wheel==0.45.1"]
build-backend = "setuptools.build_meta"

[project]
name = "rotkehlchen"
authors = [
    {name = "Rotki Solutions GmbH", email = "<EMAIL>"},
]
description = "Accounting, asset management and tax report helper for cryptocurrencies"
readme = "README.md"
requires-python = ">=3.11"
keywords = ["accounting tax-report", "portfolio asset-management", "cryptocurrencies"]
classifiers = [
    "Development Status :: 1 - Planning",
    "Topic :: Utilities",
]
license = {file = "LICENSE.md"}
dynamic = ["dependencies", "version"]

[project.urls]
homepage = "https://rotki.com"
documentation = "https://docs.rotki.com"
repository = "https://github.com/rotki/rotki"
changelog = "https://github.com/rotki/rotki/blob/develop/docs/changelog.rst"

[tool.setuptools.packages.find]
where = ["."]  # list of folders that contain the packages (["."] by default)
include = ["rotkehlchen*"]

[project.scripts]
rotkehlchen = "rotkehlchen.__main__:main"
rotkehlchen_mock = "rotkehlchen_mock.__main__:main"

[tool.setuptools.dynamic]
dependencies = {file = ["requirements.txt"]}

[tool.setuptools_scm]
fallback_version = "1.38.4"

[tool.setuptools.package-data]
"rotkehlchen.data" = ["*"]
"rotkehlchen.chain" = ["ethereum/modules/dxdaomesa/data/contracts.json"]

# -- Mypy config section --

[tool.mypy]
# Docs: https://mypy.readthedocs.io/en/latest/config_file.html
ignore_missing_imports = true
check_untyped_defs = true
disallow_untyped_defs = true
warn_unused_configs = true
warn_unused_ignores = true
warn_unreachable = true
warn_redundant_casts = true
disallow_untyped_decorators = true
disallow_untyped_calls = true
mypy_path="./stubs/"

[[tool.mypy.overrides]]
module = "rotkehlchen.*"
ignore_missing_imports = false

# -- These modules still need to have proper type definitions given --
[[tool.mypy.overrides]]
module = "rotkehlchen.tests.*"
check_untyped_defs = false
disallow_untyped_defs = false
# Pytest's fixture decorators are not typed
disallow_untyped_decorators = false

# -- Typing has been fixed in these tests --
[[tool.mypy.overrides]]
module = [
    "rotkehlchen.tests.api.blockchain.*",
    "rotkehlchen.tests.api.test_aave",
    "rotkehlchen.tests.api.test_accounting_rules",
    "rotkehlchen.tests.api.test_addressbook",
    "rotkehlchen.tests.api.test_assets",
    "rotkehlchen.tests.api.test_assets_updates",
    "rotkehlchen.tests.api.test_async",
    "rotkehlchen.tests.api.test_balancer",
    "rotkehlchen.tests.api.test_balances",
    "rotkehlchen.tests.api.test_bitcoin",
    "rotkehlchen.tests.api.test_caching",
    "rotkehlchen.tests.api.test_calendar",
    "rotkehlchen.tests.api.test_compound",
    "rotkehlchen.tests.api.test_current_assets_price",
    "rotkehlchen.tests.api.test_custom_assets",
    "rotkehlchen.tests.api.test_data_import",
    "rotkehlchen.tests.api.test_data_purging",
    "rotkehlchen.tests.api.test_database",
    "rotkehlchen.tests.api.test_defi",
    "rotkehlchen.tests.api.test_ens",
    "rotkehlchen.tests.api.test_erc20_info",
    "rotkehlchen.tests.api.test_errors",
    "rotkehlchen.tests.api.test_eth2",
    "rotkehlchen.tests.api.test_ethereum_transactions",
    "rotkehlchen.tests.api.test_evm_transactions",
    "rotkehlchen.tests.api.test_evmlike",
    "rotkehlchen.tests.api.test_exchange_rates_query",
    "rotkehlchen.tests.api.test_exchanges",
    "rotkehlchen.tests.api.test_external_services",
    "rotkehlchen.tests.api.test_historical_assets_price",
    "rotkehlchen.tests.api.test_history_base_entry",
    "rotkehlchen.tests.api.test_history_events_export",
    "rotkehlchen.tests.api.test_history",
    "rotkehlchen.tests.api.test_icons",
    "rotkehlchen.tests.api.test_ignored_actions",
    "rotkehlchen.tests.api.test_liquity",
    "rotkehlchen.tests.api.test_location_asset_mappings",
    "rotkehlchen.tests.api.test_locations",
    "rotkehlchen.tests.api.test_periodic",
    "rotkehlchen.tests.api.test_pickle",
    "rotkehlchen.tests.api.test_pnl_csv",
    "rotkehlchen.tests.api.test_premium",
    "rotkehlchen.tests.api.test_settings",
    "rotkehlchen.tests.api.test_skipped_events",
    "rotkehlchen.tests.api.test_snapshots",
    "rotkehlchen.tests.api.test_statistics",
    "rotkehlchen.tests.api.test_substrate_manager",
    "rotkehlchen.tests.api.test_sushiswap",
    "rotkehlchen.tests.data_migrations.test_migrations",
    "rotkehlchen.tests.api.test_tags",
    "rotkehlchen.tests.api.test_trades",
    "rotkehlchen.tests.api.test_uniswap",
    "rotkehlchen.tests.api.test_user_assets",
    "rotkehlchen.tests.api.test_user_evm_tokens",
    "rotkehlchen.tests.api.test_user_notes",
    "rotkehlchen.tests.api.test_users",
    "rotkehlchen.tests.api.test_yearn_vaults",
]
check_untyped_defs = true
disallow_untyped_defs = true

[[tool.mypy.overrides]]
module = "rotkehlchen.tests.fixtures.google"
ignore_errors = true  # since this is seen differently locally with requirements dev and differently in the CI with only requirements lint

# custom pylint checkers still need to be typed
[[tool.mypy.overrides]]
module = "tools.pylint.*"
check_untyped_defs = false
disallow_untyped_defs = false

# profiling is not typed
[[tool.mypy.overrides]]
module = "tools.profiling.*"
check_untyped_defs = false
disallow_untyped_defs = false

# -- ruff config section --

[tool.ruff]
line-length = 99

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]
target-version = "py311"

[tool.ruff.lint]
preview = true  # https://docs.astral.sh/ruff/preview/
# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

select = [
    "E",
    "F",
    "W",
    "C",
    "I",  # isort
    # "D",  # pydocstyle -- too many docstuff we don't use
    "N",
    "B",
    "C4",
    "T10",
    "EXE",
    "G",
    # "EM",  #  our exceptions use f strings everywhere. Very little upside to this and I don't think it's cleaner code
    # "CPY",  # If we ever wanted to enforce copyright notice on each file
    "ISC",
    "ICN",
    "T",
    "UP",
    "YTT",
    "ASYNC",
    # "ANN",  # flake8-annotations -- looks like mypy already covers this?
    "S",
    # "BLE",  # blind-except -- already pylint does this and we got ignores where needed
    # "FBT",  # FBT -- not sure I agree with the concept of boolean trap
    "A",
    "Q",  # configured for our quoting style
    "ERA",
    "PGH",
    "RET",
    "SIM",
    "SLOT",
    "FA",
    #  "SLF",  # Private member access -- we unfortunately do that some times
    # "TD",  # this is just to detect todos
    # "FIX",  # this is just to detect todos
    # "TID",  # tidy-imports -- we use relative imports from parent in some cases. Maybe stop?
    # "ARG",  # flake8-unused-arguments -- pylint already does this and we got ignores where needed
    "PL",  # all pylint rules
    "PIE",
    "PYI",
    "COM",
    "DTZ",
    "INP",
    "PT",
    "TC",
    "TCH",
    # "PTH",  # Should do it at some point. Many hits and no autofix. When enabled fully remove from extend-select
    "INT",
    "TRY",
    "FLY",
    "PERF",
    "FURB",
    "RSE",
    "LOG",
    "RUF",
]
extend-select = [
    "Q002",   # Single quote docstring found but double quotes preferred
    "Q003",   # Change outer quotes to avoid escaping inner quotes
    # Some pathlib enforcement rules follow
    "PTH108",
    "PTH109",
]
ignore = [
    "A005",  # shadow builtin module is fine, since it's easy to differentiate and we can use names like csv.py, json.py, types.py under subdirectories
    "B028",  # no explicit stack level argument for warnings. Is fine due to the way we use them for now
    "E402",  # module level import at file top. https://www.flake8rules.com/rules/E402.html
    "N818",  # error suffix in exception names
    "C901",  # function too complex
    "RUF005",  # Unpacking over concatenation -> https://github.com/charliermarsh/ruff/issues/2054#issuecomment-1399617413
    "RET501",  # Explicitly returning None
    "RET505",  # https://github.com/charliermarsh/ruff/issues/1035
    "RET506",  # https://github.com/charliermarsh/ruff/issues/1035
    "RET507",  # https://github.com/charliermarsh/ruff/issues/1035
    "RET508",  # https://github.com/charliermarsh/ruff/issues/1035
    "PGH003",  # we do use blanket type: ignore. Perhaps can fix in the future
    "SIM108",  # we do use ternary operator when it makes sense. But forcing it everywhere makes the code unreadable.
    "PLR2004", # we try to use constant vars where possible but this complains abouttoo many things
    "PLR0904", # too many public methods. This is a bit too much to enforce in the codebase
    "PLR0913", # too many arguments to function call. This is a bit too much to enforce in the codebase
    "PLR0914", # too many local variables. This is a bit too much to enforce in the codebase
    "PLR0915", # too many statements -- we probably could use this ... but with custom statements number?
    "PLR0916", # too many boolean expressions -- we dont care
    "PLR0911", # too many return statements -- we dont care
    "PLR0912", # too many branches -- we dont care
    "PLR0917", # too many positional arguments -- we dont care
    "PLR1702", # too many nested blocks -- we dont care
    "PLR6201", # Doesn't work only for constants: https://github.com/astral-sh/ruff/issues/8322
    "PLR6301", # could be a staticmethod or function -- breaks for interfaces
    "PLC0415", # we need to fix non top level imports and enable this
    "PLC1901", # we prefer direct comparison to empty string for explicitness
    "PLC2701", # unfortunately we do import private methods/vars from web3.py and webargs
    "PLC2801", # false positive complain for dunder set (though we use it in a hacky way)
    # Some bandit rules we don't check for
    "S101",  # asserts are fine
    "S103",  # permissive mask on file/directory
    "S105",  # hard coded password checks got false positives
    "S106",  # hard coded password checks got false positives
    "S311",  # We know pseudo-random is not safe for cryptographic operations
    "S324",  # insecure hash function. We use it for image md5 for etag. TODO: Change and enable?
    "S608",  # possible sql-injection is overkill since we control the variables in question
    "G004",  # Our logs do use f-strings everywhere at least for now
    # Some pytest-styles rules we don't check for
    "PT011", # pytest.raises() too broad is fine, since there is functions that raise ValueError
    "PT018", # composite assertions are fine
    "TRY003",  # long messages outside the exception class. Perhaps enable when we do internationalization and need to put messages in class logic?
    "TRY004",  # Prefer TypeError for wrong type. May be okay but would need to invetigate all usages of suggested changes
    "TRY301",  # Abstract raise to an inner function -- not sure what this? Use exception for control flow?
    "TRY400",  # logging.exception instead of logging.error. Not sure I understood this one
]

[tool.ruff.lint.flake8-quotes]
avoid-escape = true
docstring-quotes = "double"
inline-quotes = "single"
multiline-quotes = "double"

[tool.ruff.lint.isort]
combine-as-imports = true
known-third-party = ["packaging"]  # packaging is otherwise detected as first-party due to the ./packaging folder

[tool.ruff.lint.per-file-ignores]
"tools/*" = [
    "T201",  # got prints in tools
    "INP001",  # no need for __init__ in tools
]
"rotkehlchen/__main__.py" = ["T201"]  # got prints in main
"rotkehlchen/api/server.py" = ["T201"]  # got prints in server.py
"rotkehlchen/args.py" = ["T201"]  # got prints in args.py
"rotkehlchen/db/minimized_schema.py" = [
    "E501",  # huge lines there
    "Q000",  # double quoted strings needed here
]
"rotkehlchen/globaldb/minimized_schema.py" = [
    "E501",  # huge lines there
    "Q000",  # double quoted strings needed here
]
"rotkehlchen/tests/*" = [
    "S113",    # tests have no timeout in requests
    "RUF018",  # We have assignments in assert in tests and that's fine there
]
"rotkehlchen/tests/conftest.py" = [
    "S602",  # test setup. No problem with shell=True
    "S605",  # test setup, can't inject in process
    "S607",  # test setup, partial path does not mater
]
"rotkehlchen/tests/integration/test_backend.py" = [
    "S603",  # test called by us. No variable input
    "S607",  # test called by us. Partial executable path is fine.
]
"setup.py" = ["INP001"]  # no need for __init__ here
"package.py" = [
    "INP001",  # no need for __init__ here
    "S602",  # script called by us only when packaging. No problem with shell=True
    "S607",  # script called by us only when packaging. No problem with partial path
]
"tools/scripts/pylint_useless_suppression.py" = [
    "S603",  # Script called by us. No variable input
    "S607",  # Script called by us. Partial executable path is fine.
]
"docs/conf.py" = [
    "ERA001",  # Lots of comments with text combined with commented-out example code
    "INP001",  # no need for __init__ here
]
"packaging/docker/entrypoint.py" = [
    "S",  # fixme
]


[tool.vulture]

ignore_names = ["fixture_*", "*Accountant", "*Decoder"]
sort_by_size = true


[tool.pyright]

reportUndefinedVariable = "error"
reportGeneralTypeIssues = false
reportUnknownParameterType = false
reportMissingTypeStubs = false
reportMissingImports = false
reportUnknownMemberType = false
reportUnknownVariableType = false
reportUntypedFunctionDecorator = false
reportUnknownArgumentType = false
reportMissingTypeArgument = false
reportInvalidTypeVarUse = false
reportCallInDefaultInitializer = false
reportAttributeAccessIssue = false
reportPrivateImportUsage = false
reportArgumentType = false
reportIncompatibleMethodOverride = false
reportOperatorIssue = "error"
reportIndexIssue = false
reportOptionalMemberAccess = false
reportCallIssue = false
reportOptionalSubscript = false
reportInvalidTypeForm = false
reportIncompatibleVariableOverride = false
reportReturnType = false
reportOverlappingOverload = false
reportTypedDictNotRequiredAccess = "error"
reportOptionalContextManager = false
reportAssignmentType = false

exclude = ["**/tests"]
pythonVersion = "3.11"
